from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel
from app.db.database import get_db
from app.models.models import User, UserRole
import hashlib

router = APIRouter(
    prefix="/users",
    tags=["users"],
)

# Pydantic模型
class UserBase(BaseModel):
    username: str
    display_name: Optional[str] = None
    role: Optional[UserRole] = UserRole.OPERATOR

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    display_name: Optional[str] = None
    role: Optional[UserRole] = None
    password: Optional[str] = None

class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    id: int
    username: str
    display_name: str
    role: UserRole

# 工具函数
def hash_password(password: str) -> str:
    """对密码进行简单的哈希处理"""
    return hashlib.md5(password.encode()).hexdigest()

# API路由
@router.post("/login")
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """用户登录"""
    user = db.query(User).filter(User.username == login_data.username).first()
    
    if not user or hash_password(login_data.password) != user.password:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    return {
        "id": user.id,
        "username": user.username,
        "display_name": user.display_name,
        "role": user.role
    }

@router.get("/list", response_model=List[UserResponse])
async def get_users_list(
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    users = db.query(User).all()
    return users

@router.post("/", response_model=UserResponse)
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    """创建新用户"""
    # 检查用户名是否已存在
    existing = db.query(User).filter(User.username == user.username).first()
    if existing:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    hashed_password = hash_password(user.password)
    db_user = User(
        username=user.username,
        password=hashed_password,
        display_name=user.display_name or user.username,
        role=user.role
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user: UserUpdate,
    db: Session = Depends(get_db)
):
    """更新用户信息"""
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    update_data = user.dict(exclude_unset=True)
    
    if "password" in update_data and update_data["password"]:
        update_data["password"] = hash_password(update_data["password"])
    
    for key, value in update_data.items():
        setattr(db_user, key, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db)
):
    """删除用户"""
    # 不允许删除最后一个管理员
    if db.query(User).filter(User.role == UserRole.ADMIN).count() <= 1:
        admin = db.query(User).filter(User.id == user_id, User.role == UserRole.ADMIN).first()
        if admin:
            raise HTTPException(status_code=400, detail="无法删除最后一个管理员账号")
    
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    db.delete(db_user)
    db.commit()
    return {"message": "用户删除成功"} 