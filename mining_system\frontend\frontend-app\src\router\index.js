import { createRouter, createWebHistory } from 'vue-router'
import ImportView from '../views/import/ImportView.vue'
import SalesList from '../views/sales/SalesList.vue'
import CustomerList from '../views/customers/CustomerList.vue'
import VehicleList from '../views/vehicles/VehicleList.vue'
import ReportCenter from '../views/reports/ReportCenter.vue'
import LoginView from '../views/auth/LoginView.vue'
import UserManageView from '../views/auth/UserManageView.vue'
import ProductTypeList from '../views/products/ProductTypeList.vue'
import SettingsView from '../views/settings/SettingsView.vue'
import authStore from '../store/auth'

// 创建一个简单的首页组件
const Home = {
  template: `
    <div class="home-container">
      <h2>欢迎使用矿山石料销售管理系统</h2>
      <p>请从左侧菜单选择功能</p>
    </div>
  `
}

const routes = [
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'home',
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: '/import',
    name: 'import',
    component: ImportView,
    meta: { requiresAuth: true }
  },
  {
    path: '/sales',
    name: 'sales',
    component: SalesList,
    meta: { requiresAuth: true }
  },
  {
    path: '/customers',
    name: 'customers',
    component: CustomerList,
    meta: { requiresAuth: true }
  },
  {
    path: '/vehicles',
    name: 'vehicles',
    component: VehicleList,
    meta: { requiresAuth: true }
  },
  {
    path: '/reports',
    name: 'reports',
    component: ReportCenter,
    meta: { requiresAuth: true }
  },
  {
    path: '/users',
    name: 'users',
    component: UserManageView,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/products',
    name: 'products',
    component: ProductTypeList,
    meta: { requiresAuth: true }
  },
  {
    path: '/settings',
    name: 'settings',
    component: SettingsView,
    meta: { requiresAuth: true, requiresAdmin: true }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由导航守卫
router.beforeEach((to, from, next) => {
  const isAuthenticated = authStore.isAuthenticated.value
  
  // 检查是否需要身份验证
  if (to.meta.requiresAuth && !isAuthenticated) {
    // 未登录，重定向到登录页
    next({ name: 'login' })
  } 
  // 检查是否需要管理员权限
  else if (to.meta.requiresAdmin && authStore.userRole.value !== 'admin') {
    // 不是管理员，回到首页
    next({ name: 'home' })
  }
  // 已登录用户访问登录页，重定向到首页
  else if (to.name === 'login' && isAuthenticated) {
    next({ name: 'home' })
  }
  else {
    next()
  }
})

export default router 