#!/bin/bash

# 矿山石料销售管理系统 - 登录问题修复脚本
# 版本: v1.0
# 创建日期: 2025-01-28

echo "=== 登录问题诊断和修复 ==="

# 1. 检查服务状态
echo "1. 检查服务状态..."
echo "后端服务 (8087端口):"
netstat -tlnp | grep 8087
echo "前端服务 (8086端口):"
netstat -tlnp | grep 8086

# 2. 测试后端API直接访问
echo ""
echo "2. 测试后端API直接访问..."
curl -X POST http://localhost:8087/users/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  -w "\n状态码: %{http_code}\n" \
  -s

# 3. 测试前端代理
echo ""
echo "3. 测试前端代理..."
curl -X POST http://localhost:8086/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  -w "\n状态码: %{http_code}\n" \
  -s

# 4. 检查数据库用户数据
echo ""
echo "4. 检查数据库用户数据..."
cd /opt/mining_system/backend
if [ -f "app.db" ]; then
    python3 -c "
import sqlite3
import hashlib
conn = sqlite3.connect('app.db')
cursor = conn.cursor()
try:
    cursor.execute('SELECT username, password, role FROM users;')
    users = cursor.fetchall()
    print('数据库中的用户:')
    for user in users:
        print(f'  用户名: {user[0]}, 密码哈希: {user[1][:10]}..., 角色: {user[2]}')
    
    # 验证admin密码哈希
    admin_hash = hashlib.md5('admin123'.encode()).hexdigest()
    print(f'admin123的MD5哈希: {admin_hash}')
except Exception as e:
    print(f'数据库查询错误: {e}')
finally:
    conn.close()
"
else
    echo "数据库文件不存在"
fi

# 5. 检查前端vite配置
echo ""
echo "5. 检查前端vite配置..."
FRONTEND_DIR="/opt/mining_system/frontend/frontend-app"
if [ -f "$FRONTEND_DIR/vite.config.js" ]; then
    echo "vite.config.js内容:"
    cat "$FRONTEND_DIR/vite.config.js"
else
    echo "vite.config.js文件不存在"
fi

# 6. 查看日志
echo ""
echo "6. 查看服务日志..."
echo "后端日志 (最后10行):"
tail -10 /opt/mining_system/backend/backend.log 2>/dev/null || echo "后端日志文件不存在"

echo ""
echo "前端日志 (最后10行):"
tail -10 "$FRONTEND_DIR/frontend.log" 2>/dev/null || echo "前端日志文件不存在"

echo ""
echo "=== 诊断完成 ==="
