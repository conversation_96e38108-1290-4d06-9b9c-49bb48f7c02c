from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.db.database import engine, Base, get_db
from app.api import import_router, sales_router, customer_router, vehicle_router, report_router, user_router, product_router, setting_router
from app.utils.init_db import init_users
from app.api.setting_router import init_settings

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 初始化默认用户
db = next(get_db())
init_users(db)

# 初始化系统设置
init_settings(db)

# 创建FastAPI应用
app = FastAPI(
    title="矿山石料销售管理系统",
    description="综合信息管理系统API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加路由
app.include_router(import_router.router)
app.include_router(sales_router.router)
app.include_router(customer_router.router)
app.include_router(vehicle_router.router)
app.include_router(report_router.router)
app.include_router(user_router.router)
app.include_router(product_router.router)
app.include_router(setting_router.router)

@app.get("/")
async def root():
    return {"message": "欢迎使用矿山石料销售管理系统API"} 