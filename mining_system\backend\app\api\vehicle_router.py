from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List
from datetime import date
from app.db.database import get_db
from app.models.models import Vehicle, SalesRecord, Customer
from pydantic import BaseModel

router = APIRouter(
    prefix="/vehicles",
    tags=["vehicles"],
)

class VehicleBase(BaseModel):
    plate_number: str
    driver_name: Optional[str] = None
    driver_contact: Optional[str] = None
    vehicle_type: Optional[str] = None
    max_load: Optional[float] = None  # 最大载重（吨）
    notes: Optional[str] = None

class VehicleCreate(VehicleBase):
    pass

class VehicleUpdate(VehicleBase):
    pass

class VehicleResponse(VehicleBase):
    id: int
    created_at: date
    updated_at: date
    
    class Config:
        orm_mode = True

@router.get("/list")
async def get_vehicles_list(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = None
):
    """获取车辆列表"""
    query = db.query(Vehicle)
    
    if search:
        query = query.filter(
            Vehicle.plate_number.ilike(f"%{search}%") |
            Vehicle.driver_name.ilike(f"%{search}%")
        )
    
    total = query.count()
    
    vehicles = query.order_by(Vehicle.plate_number)\
        .offset((page - 1) * page_size)\
        .limit(page_size)\
        .all()
    
    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "items": vehicles
    }

@router.post("/", response_model=VehicleResponse)
async def create_vehicle(
    vehicle: VehicleCreate,
    db: Session = Depends(get_db)
):
    """创建新车辆"""
    # 检查车牌号是否已存在
    existing = db.query(Vehicle).filter(Vehicle.plate_number == vehicle.plate_number).first()
    if existing:
        raise HTTPException(status_code=400, detail="车牌号已存在")
    
    db_vehicle = Vehicle(**vehicle.dict())
    db.add(db_vehicle)
    db.commit()
    db.refresh(db_vehicle)
    return db_vehicle

@router.put("/{vehicle_id}", response_model=VehicleResponse)
async def update_vehicle(
    vehicle_id: int,
    vehicle: VehicleUpdate,
    db: Session = Depends(get_db)
):
    """更新车辆信息"""
    db_vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
    if not db_vehicle:
        raise HTTPException(status_code=404, detail="车辆不存在")
    
    # 检查新车牌号是否与其他车辆重复
    if vehicle.plate_number != db_vehicle.plate_number:
        existing = db.query(Vehicle).filter(Vehicle.plate_number == vehicle.plate_number).first()
        if existing:
            raise HTTPException(status_code=400, detail="车牌号已存在")
    
    for key, value in vehicle.dict().items():
        setattr(db_vehicle, key, value)
    
    db.commit()
    db.refresh(db_vehicle)
    return db_vehicle

@router.delete("/{vehicle_id}")
async def delete_vehicle(
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """删除车辆"""
    # 检查是否存在关联的销售记录
    sales_count = db.query(SalesRecord)\
        .filter(SalesRecord.vehicle_id == vehicle_id)\
        .count()
    
    if sales_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除该车辆，存在{sales_count}条关联的销售记录"
        )
    
    db_vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
    if not db_vehicle:
        raise HTTPException(status_code=404, detail="车辆不存在")
    
    db.delete(db_vehicle)
    db.commit()
    return {"message": "车辆删除成功"}

@router.get("/{vehicle_id}/statistics")
async def get_vehicle_statistics(
    vehicle_id: int,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取车辆统计信息"""
    # 检查车辆是否存在
    vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
    if not vehicle:
        raise HTTPException(status_code=404, detail="车辆不存在")
    
    # 构建查询
    query = db.query(
        func.count(SalesRecord.id).label('total_deliveries'),
        func.sum(SalesRecord.net_ton).label('total_weight'),
        func.avg(SalesRecord.net_ton).label('avg_weight_per_delivery'),
        func.count(func.distinct(SalesRecord.customer_id)).label('customer_count')
    ).filter(SalesRecord.vehicle_id == vehicle_id)
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    stats = query.first()
    
    # 获取最常运送的产品类型
    product_stats = db.query(
        SalesRecord.product_size,
        func.count(SalesRecord.id).label('count')
    ).filter(
        SalesRecord.vehicle_id == vehicle_id
    ).group_by(
        SalesRecord.product_size
    ).order_by(
        func.count(SalesRecord.id).desc()
    ).first()
    
    # 获取最常服务的客户
    top_customer = db.query(
        SalesRecord.customer_id,
        func.count(SalesRecord.id).label('delivery_count')
    ).filter(
        SalesRecord.vehicle_id == vehicle_id
    ).group_by(
        SalesRecord.customer_id
    ).order_by(
        func.count(SalesRecord.id).desc()
    ).first()
    
    customer_name = None
    if top_customer:
        customer = db.query(Customer).filter(Customer.id == top_customer.customer_id).first()
        customer_name = customer.name if customer else None
    
    return {
        "plate_number": vehicle.plate_number,
        "driver_name": vehicle.driver_name,
        "total_deliveries": stats.total_deliveries or 0,
        "total_weight": float(stats.total_weight or 0),
        "avg_weight_per_delivery": float(stats.avg_weight_per_delivery or 0),
        "customer_count": stats.customer_count or 0,
        "most_delivered_product": product_stats.product_size if product_stats else None,
        "top_customer": customer_name,
        "start_date": start_date,
        "end_date": end_date
    } 