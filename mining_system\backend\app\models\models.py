from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Date, Enum
from sqlalchemy.orm import relationship
from app.db.database import Base
from datetime import datetime
import enum

class Customer(Base):
    """客户信息表"""
    __tablename__ = "customers"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    contact = Column(String, nullable=True)
    address = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联销售记录
    sales = relationship("SalesRecord", back_populates="customer")
    # 关联客户日报表
    daily_reports = relationship("CustomerDailyReport", back_populates="customer")

class Vehicle(Base):
    """车辆信息表"""
    __tablename__ = "vehicles"
    
    id = Column(Integer, primary_key=True, index=True)
    plate_number = Column(String, unique=True, index=True)  # 车牌号
    driver_name = Column(String)  # 司机姓名
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    
    # 关联销售记录
    sales = relationship("SalesRecord", back_populates="vehicle")

class ProductType(Base):
    """产品类型表"""
    __tablename__ = "product_types"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(String, nullable=True)
    
    # 关联销售记录
    sales = relationship("SalesRecord", back_populates="product_type")

class SalesRecord(Base):
    """销售记录表 - 对应总表中的每一行"""
    __tablename__ = "sales_records"
    
    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"))
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    product_type_id = Column(Integer, ForeignKey("product_types.id"))
    
    time_loaded = Column(DateTime)
    product_size = Column(String)
    gross_ton = Column(Float)
    tare_ton = Column(Float)
    net_ton = Column(Float)
    way_bill_no = Column(String)
    sale_date = Column(Date, index=True)  # 销售日期
    created_at = Column(DateTime, default=datetime.now)
    
    # 关联
    customer = relationship("Customer", back_populates="sales")
    vehicle = relationship("Vehicle", back_populates="sales")
    product_type = relationship("ProductType", back_populates="sales")

class CustomerDailyReport(Base):
    """客户日报表 - 对应客户表中的每一行"""
    __tablename__ = "customer_daily_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"))
    date = Column(Date, index=True)
    vehicle_qty = Column(Integer)
    net_ton_total = Column(Float)
    
    # 各产品类型的数量列，如1/4"、1/2"、3/4"等
    product_1_4 = Column(Float, default=0)
    product_1_2 = Column(Float, default=0)
    product_3_4 = Column(Float, default=0)
    product_crush_run = Column(Float, default=0)
    product_other = Column(Float, default=0)
    
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联
    customer = relationship("Customer", back_populates="daily_reports")

class UserRole(str, enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    MANAGER = "manager"
    OPERATOR = "operator"

class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    password = Column(String)
    display_name = Column(String)
    role = Column(Enum(UserRole), default=UserRole.OPERATOR)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class SystemSetting(Base):
    """系统设置表"""
    __tablename__ = "system_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, unique=True, index=True)
    value = Column(String)
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now) 