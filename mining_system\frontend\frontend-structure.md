# 前端项目结构

以下是使用Vue3创建前端项目后的建议结构：

```
frontend-app/
├── public/                # 公共资源
├── src/                   # 源代码
│   ├── assets/            # 静态资源
│   │   ├── common/        # 通用组件
│   │   ├── layout/        # 布局组件
│   │   └── import/        # 导入相关组件
│   ├── views/             # 页面视图
│   │   ├── dashboard/     # 仪表盘
│   │   ├── import/        # 数据导入
│   │   ├── customers/     # 客户管理
│   │   ├── vehicles/      # 车辆管理
│   │   ├── sales/         # 销售记录
│   │   └── reports/       # 报表模块
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── api/               # API请求
│   ├── utils/             # 工具函数
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
└── package.json           # 项目配置
```

## 导入模块前端组件

### 文件上传组件 (FileUploader.vue)

这是一个用于上传Excel文件的组件，用于数据导入模块：

```vue
<template>
  <div class="file-uploader">
    <el-upload
      class="upload-area"
      drag
      action=""
      :http-request="uploadFile"
      :before-upload="beforeUpload"
      :show-file-list="false"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        拖拽文件到此处或 <em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          请上传Excel格式的每日销售报表文件(.xlsx, .xls)
        </div>
      </template>
    </el-upload>

    <div v-if="uploadStatus" class="upload-status">
      <el-alert
        :title="uploadStatus.message"
        :type="uploadStatus.type"
        :closable="false"
        show-icon
      />
      
      <div v-if="uploadResult" class="upload-result">
        <h3>导入结果</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="报表日期">{{ uploadResult.report_date }}</el-descriptions-item>
          <el-descriptions-item label="总记录数">{{ uploadResult.total_records }}</el-descriptions-item>
          <el-descriptions-item label="导入成功">{{ uploadResult.imported }}</el-descriptions-item>
          <el-descriptions-item label="跳过记录">{{ uploadResult.skipped }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="uploadResult.errors && uploadResult.errors.length" class="upload-errors">
          <h4>错误信息</h4>
          <el-collapse>
            <el-collapse-item title="查看详情" name="errors">
              <ul>
                <li v-for="(error, index) in uploadResult.errors" :key="index">
                  {{ error }}
                </li>
              </ul>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
import { uploadDailySalesReport } from '@/api/import'

const uploadStatus = ref(null)
const uploadResult = ref(null)

// 上传前验证
const beforeUpload = (file) => {
  const isExcel = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  
  uploadStatus.value = {
    type: 'info',
    message: '正在上传文件...'
  }
  
  return true
}

// 自定义上传方法
const uploadFile = async (options) => {
  try {
    const formData = new FormData()
    formData.append('file', options.file)
    
    uploadStatus.value = {
      type: 'info',
      message: '正在处理数据...'
    }
    
    const res = await uploadDailySalesReport(formData)
    
    uploadResult.value = res
    uploadStatus.value = {
      type: 'success',
      message: '文件导入成功!'
    }
  } catch (error) {
    uploadStatus.value = {
      type: 'error',
      message: `导入失败: ${error.message || '未知错误'}`
    }
    uploadResult.value = null
  }
}
</script>

<style scoped>
.file-uploader {
  max-width: 800px;
  margin: 0 auto;
}

.upload-area {
  margin-bottom: 20px;
}

.upload-status {
  margin-top: 20px;
}

.upload-result {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.upload-errors {
  margin-top: 15px;
}
</style>
```

### 导入页面 (ImportView.vue)

```vue
<template>
  <div class="import-container">
    <el-page-header title="返回" @back="goBack">
      <template #content>
        <span class="page-title">数据导入</span>
      </template>
    </el-page-header>
    
    <div class="import-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>导入每日销售报表</span>
          </div>
        </template>
        <file-uploader />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import FileUploader from '@/components/import/FileUploader.vue'

const router = useRouter()

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.import-container {
  padding: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
}

.import-content {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
```

### API请求文件 (api/import.js)

```javascript
import axios from 'axios'

const API_URL = 'http://localhost:8000'

export const uploadDailySalesReport = (formData) => {
  return axios.post(`${API_URL}/import/daily-sales-report/`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then(response => response.data)
}
```

## 使用方式

1. 按照README中的步骤创建Vue3项目
2. 安装Element Plus组件库: `npm install element-plus @element-plus/icons-vue`
3. 安装Axios: `npm install axios`
4. 创建上述文件结构和组件
5. 配置路由导向导入页面
6. 启动开发服务器: `npm run dev` 