# 矿山石料销售管理系统

综合信息管理系统，用于管理矿山石料销售数据，包括客户信息、销售记录、车辆信息和付款管理等。

## 系统架构

* 后端: FastAPI + SQLite3
* 前端: Vue3

## 项目结构

```
mining_system/
├── backend/             # 后端项目
│   ├── app/             # 应用代码
│   │   ├── api/         # API路由
│   │   ├── db/          # 数据库配置
│   │   ├── models/      # 数据模型
│   │   ├── services/    # 业务逻辑
│   │   └── utils/       # 工具函数
│   ├── requirements.txt # 依赖项
│   └── run.py           # 启动脚本
└── frontend/            # 前端项目
    └── frontend-app/    # Vue3应用
```

## 后端设置

1. 创建虚拟环境:
```bash
cd backend
python -m venv venv
venv\Scripts\activate  # Windows
# 或
source venv/bin/activate  # Linux/Mac
```

2. 安装依赖:
```bash
pip install -r requirements.txt
```

3. 启动后端服务:
```bash
python run.py
```

后端服务将在 http://localhost:8000 上运行，API文档可以在 http://localhost:8000/docs 访问。

## 前端设置

1. 创建Vue3项目:
```bash
cd frontend
npm init vite@latest frontend-app -- --template vue
cd frontend-app
```

2. 安装依赖:
```bash
npm install
npm install axios element-plus
```

3. 启动开发服务器:
```bash
npm run dev
```

前端应用将在 http://localhost:5173 上运行。

## 已实现功能

### 数据导入模块

- Excel文件上传和解析
- 自动提取销售数据
- 生成客户日报表

## 待实现功能

- 客户管理模块
- 车队管理模块
- 销售记录模块
- 付款管理模块
- 报表模块 