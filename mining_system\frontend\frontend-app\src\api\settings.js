import axios from 'axios'

const API_URL = '/api'

// 获取所有系统设置
export const getAllSettings = () => {
  return axios.get(`${API_URL}/settings/`)
}

// 获取单个系统设置
export const getSetting = (key) => {
  return axios.get(`${API_URL}/settings/${key}`)
}

// 更新系统设置
export const updateSetting = (key, data) => {
  return axios.put(`${API_URL}/settings/${key}`, data)
}

// 创建数据库备份
export const createBackup = () => {
  return axios.post(`${API_URL}/settings/backup`)
}

// 获取备份列表
export const getBackupsList = () => {
  return axios.get(`${API_URL}/settings/backup/list`)
}

// 重置数据库
export const resetDatabase = () => {
  return axios.post(`${API_URL}/settings/reset`)
}

// 获取数据库统计信息
export const getDatabaseStats = () => {
  return axios.get(`${API_URL}/settings/stats`)
} 