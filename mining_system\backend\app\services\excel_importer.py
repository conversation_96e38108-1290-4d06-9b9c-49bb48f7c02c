import pandas as pd
import numpy as np
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.models import Customer, Vehicle, ProductType, SalesRecord, CustomerDailyReport
from typing import Dict, List, Tuple, Any, Optional

class ExcelImporter:
    """Excel数据导入服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def import_daily_sales_report(self, file_path: str) -> Dict[str, Any]:
        """
        导入总表数据(Daily Sales Report)
        
        参数:
            file_path: Excel文件路径
            
        返回:
            导入结果统计
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, header=2)  # 第三行为表头
            
            # 获取日期信息（在第二行）
            date_df = pd.read_excel(file_path, header=None, nrows=2)
            date_str = date_df.iloc[1, 0].split(':')[1].strip()
            report_date = datetime.strptime(date_str, "%d/%m/%Y").date()
            
            # 清理数据
            df = df.replace({np.nan: None})
            
            # 导入数据
            success_count = 0
            skipped_count = 0
            errors = []
            
            for _, row in df.iterrows():
                try:
                    # 处理客户数据
                    customer_name = row.get('CUSTOMER')
                    if not customer_name:
                        skipped_count += 1
                        continue
                        
                    customer = self._get_or_create_customer(customer_name)
                    
                    # 处理车辆数据
                    plate_number = row.get('VEHICLE ID')
                    vehicle = self._get_or_create_vehicle(plate_number)
                    
                    # 处理产品类型
                    product_name = row.get('PRODUCT')
                    product_type = self._get_or_create_product_type(product_name)
                    
                    # 创建销售记录
                    time_loaded_str = row.get('TIME LOADED')
                    time_loaded = None
                    if time_loaded_str:
                        # 尝试解析时间格式
                        try:
                            time_loaded = datetime.strptime(time_loaded_str, "%H:%M")
                            # 将时间与报表日期合并
                            time_loaded = datetime.combine(report_date, time_loaded.time())
                        except:
                            pass
                    
                    sales_record = SalesRecord(
                        customer_id=customer.id,
                        vehicle_id=vehicle.id,
                        product_type_id=product_type.id,
                        time_loaded=time_loaded,
                        product_size=row.get('SIZE/MM'),
                        gross_ton=row.get('GROSS/TON'),
                        tare_ton=row.get('TARE/TON'),
                        net_ton=row.get('NET/TON'),
                        way_bill_no=row.get('WAY BILL NO.'),
                        sale_date=report_date
                    )
                    
                    self.db.add(sales_record)
                    success_count += 1
                    
                except Exception as e:
                    errors.append(f"行 {_+1} 导入错误: {str(e)}")
                    skipped_count += 1
            
            # 提交事务
            self.db.commit()
            
            # 更新客户日报表
            self._update_customer_daily_reports(report_date)
            
            return {
                "success": True,
                "total_records": len(df),
                "imported": success_count,
                "skipped": skipped_count,
                "errors": errors,
                "report_date": report_date.strftime("%Y-%m-%d")
            }
            
        except Exception as e:
            self.db.rollback()
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_or_create_customer(self, customer_name: str) -> Customer:
        """获取或创建客户记录"""
        customer = self.db.query(Customer).filter(Customer.name == customer_name).first()
        if not customer:
            customer = Customer(name=customer_name)
            self.db.add(customer)
            self.db.flush()  # 获取ID但不提交事务
        return customer
    
    def _get_or_create_vehicle(self, plate_number: str) -> Vehicle:
        """获取或创建车辆记录"""
        vehicle = self.db.query(Vehicle).filter(Vehicle.plate_number == plate_number).first()
        if not vehicle:
            vehicle = Vehicle(plate_number=plate_number)
            self.db.add(vehicle)
            self.db.flush()  # 获取ID但不提交事务
        return vehicle
    
    def _get_or_create_product_type(self, product_name: str) -> ProductType:
        """获取或创建产品类型记录"""
        product_type = self.db.query(ProductType).filter(ProductType.name == product_name).first()
        if not product_type:
            product_type = ProductType(name=product_name)
            self.db.add(product_type)
            self.db.flush()  # 获取ID但不提交事务
        return product_type
    
    def _update_customer_daily_reports(self, report_date) -> None:
        """更新客户日报表数据"""
        # 获取指定日期的所有销售记录
        sales = (self.db.query(SalesRecord)
                .filter(SalesRecord.sale_date == report_date)
                .all())
        
        # 按客户分组统计
        customer_data = {}
        for sale in sales:
            if sale.customer_id not in customer_data:
                customer_data[sale.customer_id] = {
                    "vehicle_qty": 0,
                    "net_ton_total": 0,
                    "product_1_4": 0,
                    "product_1_2": 0,
                    "product_3_4": 0,
                    "product_crush_run": 0,
                    "product_other": 0
                }
            
            # 更新车辆数量和总净重
            customer_data[sale.customer_id]["vehicle_qty"] += 1
            
            if sale.net_ton:
                customer_data[sale.customer_id]["net_ton_total"] += sale.net_ton
            
            # 根据产品规格更新对应产品量
            if sale.product_size:
                size = sale.product_size.strip().lower()
                if "1/4" in size:
                    customer_data[sale.customer_id]["product_1_4"] += (sale.net_ton or 0)
                elif "1/2" in size:
                    customer_data[sale.customer_id]["product_1_2"] += (sale.net_ton or 0)
                elif "3/4" in size:
                    customer_data[sale.customer_id]["product_3_4"] += (sale.net_ton or 0)
                elif "crush run" in size or "0-40" in size:
                    customer_data[sale.customer_id]["product_crush_run"] += (sale.net_ton or 0)
                else:
                    customer_data[sale.customer_id]["product_other"] += (sale.net_ton or 0)
        
        # 更新或创建每个客户的日报表
        for customer_id, data in customer_data.items():
            report = (self.db.query(CustomerDailyReport)
                     .filter(CustomerDailyReport.customer_id == customer_id, 
                             CustomerDailyReport.date == report_date)
                     .first())
            
            if not report:
                report = CustomerDailyReport(
                    customer_id=customer_id,
                    date=report_date,
                    **data
                )
                self.db.add(report)
            else:
                # 更新已有记录
                for key, value in data.items():
                    setattr(report, key, value)
        
        # 提交更改
        self.db.commit() 