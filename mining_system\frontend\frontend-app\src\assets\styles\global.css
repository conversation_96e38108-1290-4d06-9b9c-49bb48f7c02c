/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.el-table {
  width: 100% !important;
}

.el-table__body-wrapper {
  overflow-x: hidden !important;
}

.el-table__header-wrapper {
  width: 100% !important;
}

.el-card {
  width: 100% !important;
}

/* 确保所有元素都不会超出容器 */
* {
  box-sizing: border-box;
  max-width: 100%;
}

/* 移除所有可能的水平滚动条 */
::-webkit-scrollbar-horizontal {
  display: none;
}

/* 修改垂直滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
} 