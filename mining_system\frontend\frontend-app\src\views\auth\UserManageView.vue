<template>
  <div class="user-container">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">新增用户</el-button>
        <el-button type="primary" @click="fetchUsers">刷新数据</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="users"
      border
      style="width: 100%"
    >
      <el-table-column prop="username" label="用户名" min-width="120" />
      <el-table-column prop="display_name" label="显示名称" min-width="150" />
      <el-table-column prop="role" label="角色" width="120">
        <template #default="{ row }">
          <el-tag :type="getRoleTagType(row.role)">
            {{ getRoleDisplay(row.role) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增用户' : '编辑用户'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username" v-if="dialogType === 'add'">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="显示名称" prop="display_name">
          <el-input v-model="form.display_name" placeholder="请输入显示名称" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="form.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option label="部门经理" value="manager" />
            <el-option label="操作员" value="operator" />
          </el-select>
        </el-form-item>
        <el-form-item 
          :label="dialogType === 'add' ? '密码' : '新密码'" 
          :prop="dialogType === 'add' ? 'password' : ''"
        >
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
          <span v-if="dialogType === 'edit'" class="password-hint">
            如不修改密码，请留空
          </span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUsersList, createUser, updateUser, deleteUser } from '@/api/auth'
import authStore from '@/store/auth'

// 用户数据
const users = ref([])
const loading = ref(false)

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = ref({
  username: '',
  display_name: '',
  role: 'operator',
  password: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await getUsersList()
    users.value = response.data
  } catch (error) {
    ElMessage.error('获取用户列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 获取角色显示文本
const getRoleDisplay = (role) => {
  switch (role) {
    case 'admin': return '管理员'
    case 'manager': return '部门经理'
    case 'operator': return '操作员'
    default: return role
  }
}

// 获取角色标签类型
const getRoleTagType = (role) => {
  switch (role) {
    case 'admin': return 'danger'
    case 'manager': return 'warning'
    case 'operator': return 'info'
    default: return ''
  }
}

// 新增用户
const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    username: '',
    display_name: '',
    role: 'operator',
    password: ''
  }
  dialogVisible.value = true
  
  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 编辑用户
const handleEdit = (row) => {
  dialogType.value = 'edit'
  form.value = {
    id: row.id,
    username: row.username,
    display_name: row.display_name,
    role: row.role,
    password: ''
  }
  dialogVisible.value = true
  
  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  // 编辑模式下密码可以为空
  if (dialogType.value === 'edit' && !form.value.password) {
    // 移除密码验证
    formRef.value.clearValidate('password')
  }
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await createUser(form.value)
          ElMessage.success('创建成功')
        } else {
          // 如果密码为空，则不更新密码
          const updateData = { ...form.value }
          if (!updateData.password) {
            delete updateData.password
          }
          
          await updateUser(form.value.id, updateData)
          ElMessage.success('更新成功')
          
          // 如果修改的是当前用户，则更新存储的用户信息
          if (form.value.id === authStore.currentUser.value?.id) {
            authStore.currentUser.value.display_name = form.value.display_name
            authStore.currentUser.value.role = form.value.role
            localStorage.setItem('currentUser', JSON.stringify(authStore.currentUser.value))
          }
        }
        dialogVisible.value = false
        fetchUsers()
      } catch (error) {
        ElMessage.error('操作失败：' + error.response?.data?.detail || error.message)
      }
    }
  })
}

// 删除用户
const handleDelete = (row) => {
  // 不能删除自己
  if (row.id === authStore.currentUser.value?.id) {
    ElMessage.warning('不能删除当前登录的用户')
    return
  }
  
  ElMessageBox.confirm(
    '确定要删除该用户吗？删除后无法恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteUser(row.id)
      ElMessage.success('删除成功')
      fetchUsers()
    } catch (error) {
      ElMessage.error('删除失败：' + error.response?.data?.detail || error.message)
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.password-hint {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 