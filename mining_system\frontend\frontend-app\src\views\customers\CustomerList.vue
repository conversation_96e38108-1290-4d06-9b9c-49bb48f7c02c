<template>
  <div class="customer-container">
    <div class="page-header">
      <h2>客户管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">新增客户</el-button>
        <el-button type="primary" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="客户名称">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="客户名称" min-width="150" />
      <el-table-column prop="contact" label="联系人" width="120" />
      <el-table-column prop="address" label="地址" min-width="200" />
      <el-table-column prop="created_at" label="创建时间" width="120">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleViewStats(row)"
          >
            统计
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 客户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增客户' : '编辑客户'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="form.address"
            type="textarea"
            placeholder="请输入地址"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog
      v-model="statsDialogVisible"
      title="客户统计信息"
      width="600px"
    >
      <el-form label-width="140px">
        <el-form-item label="统计时间范围">
          <el-date-picker
            v-model="statsDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleStatsDateChange"
          />
        </el-form-item>
      </el-form>
      
      <el-descriptions :column="1" border>
        <el-descriptions-item label="客户名称">
          {{ statsData.customer_name }}
        </el-descriptions-item>
        <el-descriptions-item label="总订单数">
          {{ statsData.total_orders }} 单
        </el-descriptions-item>
        <el-descriptions-item label="总销售重量">
          {{ statsData.total_weight?.toFixed(2) }} 吨
        </el-descriptions-item>
        <el-descriptions-item label="平均每单重量">
          {{ statsData.avg_weight_per_order?.toFixed(2) }} 吨
        </el-descriptions-item>
        <el-descriptions-item label="最常购买产品">
          {{ statsData.most_bought_product || '暂无数据' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getCustomersList,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerStatistics
} from '@/api/customers'

// 搜索表单
const searchForm = ref({
  search: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = ref({
  name: '',
  contact: '',
  address: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 统计信息
const statsDialogVisible = ref(false)
const statsDateRange = ref([])
const statsData = ref({})
const currentCustomerId = ref(null)

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return date.split('T')[0]
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchForm.value.search
    }
    const response = await getCustomersList(params)
    tableData.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value.search = ''
  handleSearch()
}

// 刷新数据
const refreshData = () => {
  fetchData()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 新增客户
const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    name: '',
    contact: '',
    address: ''
  }
  dialogVisible.value = true
}

// 编辑客户
const handleEdit = (row) => {
  dialogType.value = 'edit'
  form.value = { ...row }
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await createCustomer(form.value)
          ElMessage.success('创建成功')
        } else {
          await updateCustomer(form.value.id, form.value)
          ElMessage.success('更新成功')
        }
        dialogVisible.value = false
        fetchData()
      } catch (error) {
        ElMessage.error('操作失败：' + error.response?.data?.detail || error.message)
      }
    }
  })
}

// 删除客户
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除该客户吗？删除后无法恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteCustomer(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      ElMessage.error('删除失败：' + error.response?.data?.detail || error.message)
    }
  })
}

// 查看统计信息
const handleViewStats = async (row) => {
  currentCustomerId.value = row.id
  statsDialogVisible.value = true
  statsDateRange.value = []
  await fetchCustomerStats()
}

// 获取客户统计信息
const fetchCustomerStats = async () => {
  if (!currentCustomerId.value) return
  
  try {
    const params = {}
    if (statsDateRange.value?.length === 2) {
      params.start_date = statsDateRange.value[0]
      params.end_date = statsDateRange.value[1]
    }
    
    const response = await getCustomerStatistics(currentCustomerId.value, params)
    statsData.value = response.data
  } catch (error) {
    ElMessage.error('获取统计信息失败：' + error.message)
  }
}

// 统计日期范围变化
const handleStatsDateChange = () => {
  fetchCustomerStats()
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.customer-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 