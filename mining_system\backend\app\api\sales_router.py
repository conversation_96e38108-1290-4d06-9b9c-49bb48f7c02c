from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import date
from app.db.database import get_db
from app.models.models import SalesRecord
from sqlalchemy.orm import joinedload

router = APIRouter(
    prefix="/sales",
    tags=["sales"],
)

@router.get("/list")
async def get_sales_list(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    customer: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None
):
    """
    获取销售记录列表
    """
    query = db.query(SalesRecord).options(
        joinedload(SalesRecord.customer),
        joinedload(SalesRecord.vehicle),
        joinedload(SalesRecord.product_type)
    )
    
    # 应用过滤条件
    if customer:
        query = query.join(SalesRecord.customer).filter(
            SalesRecord.customer.has(name=customer)
        )
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    # 计算总数
    total = query.count()
    
    # 分页
    records = query.order_by(SalesRecord.sale_date.desc(), SalesRecord.time_loaded.desc())\
        .offset((page - 1) * page_size)\
        .limit(page_size)\
        .all()
    
    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "items": records
    } 