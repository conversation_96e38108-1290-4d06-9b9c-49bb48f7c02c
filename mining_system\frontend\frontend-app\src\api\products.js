import axios from 'axios'

const API_URL = '/api'

// 获取产品类型列表
export const getProductTypesList = (params) => {
  return axios.get(`${API_URL}/products/list`, { params })
}

// 创建新产品类型
export const createProductType = (data) => {
  return axios.post(`${API_URL}/products/`, data)
}

// 更新产品类型
export const updateProductType = (id, data) => {
  return axios.put(`${API_URL}/products/${id}`, data)
}

// 删除产品类型
export const deleteProductType = (id) => {
  return axios.delete(`${API_URL}/products/${id}`)
}

// 获取产品类型统计信息
export const getProductTypeStatistics = (id, params) => {
  return axios.get(`${API_URL}/products/${id}/statistics`, { params })
}