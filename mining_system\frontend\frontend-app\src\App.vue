<script setup>
import { RouterView, RouterLink, useRouter } from 'vue-router'
import { computed } from 'vue'
import {
  Upload,
  User,
  Van,
  ShoppingCart,
  DataAnalysis,
  Setting,
  SwitchButton,
  Goods,
  Tools
} from '@element-plus/icons-vue'
import authStore from './store/auth'

const router = useRouter()

// 当前用户信息
const currentUser = computed(() => authStore.currentUser.value)
const isAuthenticated = computed(() => authStore.isAuthenticated.value)

// 处理退出登录
const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

// 用户角色名称
const getRoleName = (role) => {
  switch (role) {
    case 'admin': return '管理员'
    case 'manager': return '部门经理'
    case 'operator': return '操作员'
    default: return '未知'
  }
}

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  if (command === 'logout') {
    handleLogout()
  } else if (command === 'users') {
    router.push('/users')
  }
}
</script>

<template>
  <el-config-provider>
    <div class="app-container">
      <template v-if="isAuthenticated">
        <el-container>
          <el-header height="60px">
            <div class="header-container">
              <div class="logo">
                <h1>矿山石料销售管理系统</h1>
              </div>
              <div class="user-info">
                <span class="user-role">
                  <el-tag size="small" :type="currentUser.role === 'admin' ? 'danger' : 'info'">
                    {{ getRoleName(currentUser.role) }}
                  </el-tag>
                </span>
                <span class="user-name">{{ currentUser.display_name || currentUser.username }}</span>
                <el-dropdown @command="handleUserCommand">
                  <el-avatar icon="el-icon-user" :size="32" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="users" v-if="currentUser.role === 'admin'">
                        <el-icon><Setting /></el-icon>
                        <span>用户管理</span>
                      </el-dropdown-item>
                      <el-dropdown-item command="logout" divided>
                        <el-icon><SwitchButton /></el-icon>
                        <span>退出登录</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </el-header>
          
          <el-container>
            <el-aside width="220px">
              <el-menu
                router
                :default-active="$route.path"
                class="el-menu-vertical"
                background-color="#2c3e50"
                text-color="#e0e0e0"
                active-text-color="#ffffff"
              >
                <div class="menu-header">
                  <span>功能导航</span>
                </div>
                
                <el-menu-item index="/import">
                  <el-icon><Upload /></el-icon>
                  <span>数据导入</span>
                </el-menu-item>
                
                <el-menu-item index="/customers">
                  <el-icon><User /></el-icon>
                  <span>客户管理</span>
                </el-menu-item>
                
                <el-menu-item index="/vehicles">
                  <el-icon><Van /></el-icon>
                  <span>车辆管理</span>
                </el-menu-item>
                
                <el-menu-item index="/products">
                  <el-icon><Goods /></el-icon>
                  <span>产品类型</span>
                </el-menu-item>
                
                <el-menu-item index="/sales">
                  <el-icon><ShoppingCart /></el-icon>
                  <span>销售记录</span>
                </el-menu-item>
                
                <el-menu-item index="/reports">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>报表中心</span>
                </el-menu-item>
                
                <div class="menu-divider"></div>
                
                <div class="menu-header">
                  <span>系统</span>
                </div>
                
                <el-menu-item index="/users" v-if="currentUser.role === 'admin'">
                  <el-icon><Setting /></el-icon>
                  <span>用户管理</span>
                </el-menu-item>

                <el-menu-item index="/settings" v-if="currentUser.role === 'admin'">
                  <el-icon><Tools /></el-icon>
                  <span>系统设置</span>
                </el-menu-item>
              </el-menu>
            </el-aside>
            
            <el-main>
              <router-view />
            </el-main>
          </el-container>
        </el-container>
      </template>
      <template v-else>
        <router-view />
      </template>
    </div>
  </el-config-provider>
</template>

<style scoped>
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.el-container {
  height: 100%;
  width: 100%;
}

.el-header {
  background-color: #2c3e50;
  color: white;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
}

.header-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: 1px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
}

.user-role {
  margin-right: 5px;
}

.el-aside {
  background-color: #2c3e50;
  color: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: width 0.3s;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.el-menu-vertical {
  height: 100%;
  border-right: none;
  background-color: #2c3e50;
}

.el-menu-vertical .el-menu-item {
  margin: 4px 10px;
  height: 50px;
  line-height: 50px;
  border-radius: 4px;
  color: #e0e0e0;
}

.el-menu-vertical .el-menu-item:hover,
.el-menu-vertical .el-menu-item.is-active {
  background-color: #34495e;
  color: white;
}

.el-menu-vertical .el-menu-item.is-active {
  border-left: 4px solid #409eff;
}

.el-menu-vertical .el-menu-item .el-icon {
  margin-right: 12px;
  font-size: 18px;
  vertical-align: middle;
}

.el-main {
  padding: 15px;
  background-color: #f9f9f9;
  overflow-y: auto;
  height: calc(100vh - 60px);
  width: 100%;
}

.menu-header {
  padding: 15px 20px 8px;
  color: #909399;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.menu-divider {
  height: 1px;
  margin: 10px 0;
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-card) {
  width: 100%;
}
</style>
