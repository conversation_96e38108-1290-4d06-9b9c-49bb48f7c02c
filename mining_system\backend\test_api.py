import requests

def test_settings_api():
    """测试设置API是否正常工作"""
    base_url = "http://localhost:8000"
    
    try:
        # 测试获取数据库统计信息
        response = requests.get(f"{base_url}/settings/stats")
        print(f"GET /settings/stats - Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error connecting to API: {str(e)}")

if __name__ == "__main__":
    test_settings_api() 