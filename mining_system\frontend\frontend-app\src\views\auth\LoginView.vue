<template>
  <div class="login-container">
    <div class="login-card">
      <h2>矿山石料销售管理系统</h2>
      <div class="login-form">
        <el-form
          ref="loginForm"
          :model="loginData"
          :rules="rules"
          label-position="top"
          @keyup.enter="handleLogin"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="loginData.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="loginData.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
          
          <div class="login-actions">
            <el-button
              type="primary"
              :loading="loading"
              @click="handleLogin"
              style="width: 100%"
            >
              登录
            </el-button>
          </div>
          
          <div class="quick-login">
            <h4>快速登录</h4>
            <div class="quick-buttons">
              <el-button @click="quickLogin('admin', 'admin123')">管理员</el-button>
              <el-button @click="quickLogin('manager', 'manager123')">经理</el-button>
              <el-button @click="quickLogin('operator', 'operator123')">操作员</el-button>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login } from '@/api/auth'
import authStore from '@/store/auth'

const router = useRouter()

const loginForm = ref(null)
const loading = ref(false)
const loginData = ref({
  username: '',
  password: ''
})

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const handleLogin = async () => {
  if (!loginForm.value) return
  
  await loginForm.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const response = await login(loginData.value.username, loginData.value.password)
        authStore.setCurrentUser(response.data)
        
        ElMessage.success('登录成功')
        router.push('/')
      } catch (error) {
        ElMessage.error(error.response?.data?.detail || '登录失败，请检查用户名和密码')
      } finally {
        loading.value = false
      }
    }
  })
}

const quickLogin = (username, password) => {
  loginData.value.username = username
  loginData.value.password = password
  handleLogin()
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f2f5;
}

.login-card {
  width: 400px;
  padding: 40px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.login-card h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #409eff;
}

.login-form {
  width: 100%;
}

.login-actions {
  margin-top: 20px;
}

.quick-login {
  margin-top: 30px;
  text-align: center;
}

.quick-login h4 {
  margin-bottom: 15px;
  font-weight: normal;
  color: #606266;
}

.quick-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.quick-buttons .el-button {
  flex: 1;
  font-size: 14px;
}
</style> 