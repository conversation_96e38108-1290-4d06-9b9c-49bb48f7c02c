<template>
  <div class="import-container">
    <el-page-header title="返回" @back="goBack">
      <template #content>
        <span class="page-title">数据导入</span>
      </template>
    </el-page-header>
    
    <div class="import-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>导入每日销售报表</span>
          </div>
        </template>
        <file-uploader />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import FileUploader from '@/components/import/FileUploader.vue'

const router = useRouter()

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.import-container {
  padding: 10px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.import-content {
  margin-top: 15px;
  width: 100%;
  max-width: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 18px;
  font-weight: 500;
  color: #409eff;
}

:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.el-card:hover) {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f9f9;
  border-radius: 8px 8px 0 0;
}

:deep(.el-page-header) {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e6e6e6;
}

:deep(.el-page-header__content) {
  font-size: 20px;
  font-weight: 600;
}

:deep(.el-page-header__back) {
  font-size: 16px;
  color: #409eff;
}
</style> 