<template>
  <div class="product-container">
    <div class="page-header">
      <h2>产品类型管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">新增产品类型</el-button>
        <el-button type="primary" @click="fetchData">刷新数据</el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="产品名称">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="产品名称" min-width="150" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleViewStats(row)"
          >
            统计
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 产品类型表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增产品类型' : '编辑产品类型'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog
      v-model="statsDialogVisible"
      title="产品统计信息"
      width="600px"
    >
      <el-form label-width="140px">
        <el-form-item label="统计时间范围">
          <el-date-picker
            v-model="statsDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleStatsDateChange"
          />
        </el-form-item>
      </el-form>
      
      <el-descriptions :column="1" border>
        <el-descriptions-item label="产品名称">
          {{ statsData.product_name }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" v-if="statsData.description">
          {{ statsData.description }}
        </el-descriptions-item>
        <el-descriptions-item label="总订单数">
          {{ statsData.total_orders }} 单
        </el-descriptions-item>
        <el-descriptions-item label="总销售重量">
          {{ statsData.total_weight?.toFixed(2) }} 吨
        </el-descriptions-item>
        <el-descriptions-item label="平均每单重量">
          {{ statsData.avg_weight_per_order?.toFixed(2) }} 吨
        </el-descriptions-item>
        <el-descriptions-item label="购买客户数">
          {{ statsData.customer_count }} 个
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getProductTypesList,
  createProductType,
  updateProductType,
  deleteProductType,
  getProductTypeStatistics
} from '@/api/products'

// 搜索表单
const searchForm = ref({
  search: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = ref({
  name: '',
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 统计信息
const statsDialogVisible = ref(false)
const statsDateRange = ref([])
const statsData = ref({})
const currentProductId = ref(null)

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      search: searchForm.value.search
    }
    const response = await getProductTypesList(params)
    tableData.value = response.data
  } catch (error) {
    ElMessage.error('获取数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value.search = ''
  handleSearch()
}

// 新增产品类型
const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    name: '',
    description: ''
  }
  dialogVisible.value = true
  
  // 延迟重置表单，确保DOM已更新
  setTimeout(() => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }, 0)
}

// 编辑产品类型
const handleEdit = (row) => {
  dialogType.value = 'edit'
  form.value = { ...row }
  dialogVisible.value = true
  
  // 延迟重置表单，确保DOM已更新
  setTimeout(() => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }, 0)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await createProductType(form.value)
          ElMessage.success('创建成功')
        } else {
          await updateProductType(form.value.id, form.value)
          ElMessage.success('更新成功')
        }
        dialogVisible.value = false
        fetchData()
      } catch (error) {
        ElMessage.error('操作失败：' + error.response?.data?.detail || error.message)
      }
    }
  })
}

// 删除产品类型
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除该产品类型吗？删除后无法恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteProductType(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      ElMessage.error('删除失败：' + error.response?.data?.detail || error.message)
    }
  })
}

// 查看统计信息
const handleViewStats = async (row) => {
  currentProductId.value = row.id
  statsDialogVisible.value = true
  statsDateRange.value = []
  await fetchProductStats()
}

// 获取产品统计信息
const fetchProductStats = async () => {
  if (!currentProductId.value) return
  
  try {
    const params = {}
    if (statsDateRange.value?.length === 2) {
      params.start_date = statsDateRange.value[0]
      params.end_date = statsDateRange.value[1]
    }
    
    const response = await getProductTypeStatistics(currentProductId.value, params)
    statsData.value = response.data
  } catch (error) {
    ElMessage.error('获取统计信息失败：' + error.message)
  }
}

// 统计日期范围变化
const handleStatsDateChange = () => {
  fetchProductStats()
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.product-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 