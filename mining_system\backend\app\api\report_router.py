from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import Optional, List
from datetime import date, timedelta
from app.db.database import get_db
from app.models.models import SalesRecord, Customer, Vehicle, ProductType
from pydantic import BaseModel

router = APIRouter(
    prefix="/reports",
    tags=["reports"],
)

class DateRangeParams(BaseModel):
    start_date: date
    end_date: date

@router.get("/sales-summary")
async def get_sales_summary(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取销售汇总报表"""
    query = db.query(
        func.date(SalesRecord.sale_date).label('date'),
        func.count(SalesRecord.id).label('order_count'),
        func.sum(SalesRecord.net_ton).label('total_weight'),
        func.count(func.distinct(SalesRecord.customer_id)).label('customer_count'),
        func.count(func.distinct(SalesRecord.vehicle_id)).label('vehicle_count')
    ).group_by(
        func.date(SalesRecord.sale_date)
    )
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    daily_stats = query.order_by(func.date(SalesRecord.sale_date)).all()
    
    # 计算合计
    totals = {
        'total_orders': sum(day.order_count for day in daily_stats),
        'total_weight': sum(day.total_weight or 0 for day in daily_stats),
        'avg_daily_orders': sum(day.order_count for day in daily_stats) / len(daily_stats) if daily_stats else 0,
        'avg_daily_weight': sum(day.total_weight or 0 for day in daily_stats) / len(daily_stats) if daily_stats else 0
    }
    
    return {
        "daily_stats": [
            {
                "date": str(day.date),
                "order_count": day.order_count,
                "total_weight": float(day.total_weight or 0),
                "customer_count": day.customer_count,
                "vehicle_count": day.vehicle_count
            }
            for day in daily_stats
        ],
        "totals": totals
    }

@router.get("/customer-analysis")
async def get_customer_analysis(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    top_n: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取客户分析报表"""
    # 基础查询
    query = db.query(
        Customer.name.label('customer_name'),
        func.count(SalesRecord.id).label('order_count'),
        func.sum(SalesRecord.net_ton).label('total_weight'),
        func.avg(SalesRecord.net_ton).label('avg_weight_per_order')
    ).join(
        SalesRecord, Customer.id == SalesRecord.customer_id
    ).group_by(
        Customer.id, Customer.name
    )
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    # 获取前N名客户
    top_customers = query.order_by(desc('total_weight')).limit(top_n).all()
    
    # 计算客户活跃度
    active_customers_query = db.query(
        func.count(func.distinct(SalesRecord.customer_id))
    ).filter(
        SalesRecord.sale_date >= (date.today() - timedelta(days=30))
    )
    
    active_customers = active_customers_query.scalar() or 0
    
    return {
        "top_customers": [
            {
                "customer_name": customer.customer_name,
                "order_count": customer.order_count,
                "total_weight": float(customer.total_weight or 0),
                "avg_weight_per_order": float(customer.avg_weight_per_order or 0)
            }
            for customer in top_customers
        ],
        "statistics": {
            "active_customers_30d": active_customers,
            "total_customers": db.query(Customer).count()
        }
    }

@router.get("/vehicle-analysis")
async def get_vehicle_analysis(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    top_n: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取车辆运营分析报表"""
    # 基础查询
    query = db.query(
        Vehicle.plate_number,
        Vehicle.driver_name,
        func.count(SalesRecord.id).label('delivery_count'),
        func.sum(SalesRecord.net_ton).label('total_weight'),
        func.avg(SalesRecord.net_ton).label('avg_weight_per_delivery')
    ).join(
        SalesRecord, Vehicle.id == SalesRecord.vehicle_id
    ).group_by(
        Vehicle.id, Vehicle.plate_number, Vehicle.driver_name
    )
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    # 获取前N名车辆
    top_vehicles = query.order_by(desc('total_weight')).limit(top_n).all()
    
    # 计算车辆利用率
    active_vehicles_query = db.query(
        func.count(func.distinct(SalesRecord.vehicle_id))
    ).filter(
        SalesRecord.sale_date >= (date.today() - timedelta(days=30))
    )
    
    active_vehicles = active_vehicles_query.scalar() or 0
    
    return {
        "top_vehicles": [
            {
                "plate_number": vehicle.plate_number,
                "driver_name": vehicle.driver_name,
                "delivery_count": vehicle.delivery_count,
                "total_weight": float(vehicle.total_weight or 0),
                "avg_weight_per_delivery": float(vehicle.avg_weight_per_delivery or 0)
            }
            for vehicle in top_vehicles
        ],
        "statistics": {
            "active_vehicles_30d": active_vehicles,
            "total_vehicles": db.query(Vehicle).count()
        }
    }

@router.get("/product-analysis")
async def get_product_analysis(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取产品销售分析报表"""
    # 按产品类型统计
    query = db.query(
        SalesRecord.product_size,
        func.count(SalesRecord.id).label('order_count'),
        func.sum(SalesRecord.net_ton).label('total_weight'),
        func.avg(SalesRecord.net_ton).label('avg_weight_per_order')
    ).group_by(
        SalesRecord.product_size
    )
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    product_stats = query.order_by(desc('total_weight')).all()
    
    # 计算产品占比
    total_weight = sum(float(stat.total_weight or 0) for stat in product_stats)
    
    return {
        "product_stats": [
            {
                "product_size": stat.product_size,
                "order_count": stat.order_count,
                "total_weight": float(stat.total_weight or 0),
                "avg_weight_per_order": float(stat.avg_weight_per_order or 0),
                "weight_percentage": round(float(stat.total_weight or 0) / total_weight * 100, 2) if total_weight > 0 else 0
            }
            for stat in product_stats
        ],
        "total_weight": total_weight
    } 