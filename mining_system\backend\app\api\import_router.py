from fastapi import APIRouter, Depends, UploadFile, File, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.excel_importer import ExcelImporter
import tempfile
import os
from typing import Dict, Any

router = APIRouter(
    prefix="/import",
    tags=["import"],
)

@router.post("/daily-sales-report/")
async def import_daily_sales_report(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    导入每日销售报表(总表)
    
    接收Excel文件上传，处理并导入到数据库
    """
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="仅支持Excel文件(.xlsx, .xls)")
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    
    try:
        # 写入上传的文件内容到临时文件
        contents = await file.read()
        temp_file.write(contents)
        temp_file.close()
        
        # 使用导入服务处理文件
        importer = ExcelImporter(db)
        result = importer.import_daily_sales_report(temp_file.name)
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])
            
        return result
    
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name) 