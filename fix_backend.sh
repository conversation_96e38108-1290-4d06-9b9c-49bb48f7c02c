#!/bin/bash

echo "=== 矿山石料销售管理系统后端修复脚本 ==="
echo "开始时间: $(date)"

# 1. 检查当前PM2状态
echo "=== 1. 检查PM2状态 ==="
pm2 status

# 2. 停止并删除错误的后端进程
echo "=== 2. 清理PM2进程 ==="
pm2 delete mining-backend 2>/dev/null || echo "没有找到mining-backend进程"
pm2 kill 2>/dev/null || echo "PM2已停止"

# 3. 清理可能的残留Python进程
echo "=== 3. 清理Python进程 ==="
echo "当前Python进程:"
ps aux | grep python | grep -v grep
echo "清理残留的Python进程..."
pkill -f "python.*run.py" 2>/dev/null || echo "没有找到相关Python进程"
pkill -f "uvicorn" 2>/dev/null || echo "没有找到uvicorn进程"

# 4. 检查端口占用
echo "=== 4. 检查端口8087占用情况 ==="
netstat -tlnp | grep 8087 || echo "端口8087未被占用"

# 5. 进入后端目录
echo "=== 5. 进入后端目录 ==="
cd /opt/mining_system/backend || {
    echo "错误: 无法进入后端目录"
    exit 1
}

# 6. 检查虚拟环境
echo "=== 6. 检查虚拟环境 ==="
if [ ! -d "venv" ]; then
    echo "错误: 虚拟环境不存在"
    exit 1
fi

# 7. 激活虚拟环境并检查依赖
echo "=== 7. 激活虚拟环境 ==="
source venv/bin/activate

echo "Python版本: $(python --version)"
echo "检查关键依赖:"
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')" 2>/dev/null || echo "FastAPI未安装"
python -c "import uvicorn; print(f'Uvicorn: {uvicorn.__version__}')" 2>/dev/null || echo "Uvicorn未安装"

# 8. 检查应用文件
echo "=== 8. 检查应用文件 ==="
if [ ! -f "run.py" ]; then
    echo "错误: run.py文件不存在"
    exit 1
fi

if [ ! -f "ecosystem.config.js" ]; then
    echo "错误: ecosystem.config.js文件不存在"
    exit 1
fi

# 9. 测试应用启动
echo "=== 9. 测试应用启动 ==="
echo "尝试启动应用进行测试..."
timeout 10 python run.py &
PYTHON_PID=$!
sleep 5

# 检查进程是否还在运行
if kill -0 $PYTHON_PID 2>/dev/null; then
    echo "应用启动成功，正在测试API..."
    # 测试API
    curl -s http://localhost:8087/ || echo "API测试失败"
    # 停止测试进程
    kill $PYTHON_PID 2>/dev/null
    wait $PYTHON_PID 2>/dev/null
    echo "测试完成，已停止测试进程"
else
    echo "应用启动失败"
    exit 1
fi

# 10. 使用PM2启动服务
echo "=== 10. 使用PM2启动服务 ==="
pm2 start ecosystem.config.js

# 11. 检查PM2状态
echo "=== 11. 检查PM2状态 ==="
sleep 3
pm2 status

# 12. 查看服务日志
echo "=== 12. 查看服务日志 ==="
pm2 logs mining-backend --lines 10

# 13. 测试API访问
echo "=== 13. 测试API访问 ==="
echo "测试本地API:"
curl -s http://localhost:8087/ | head -3 || echo "本地API测试失败"

echo "测试外部API:"
curl -s http://************:8087/ | head -3 || echo "外部API测试失败"

# 14. 保存PM2配置
echo "=== 14. 保存PM2配置 ==="
pm2 save

echo "=== 修复完成 ==="
echo "结束时间: $(date)"
echo ""
echo "请在浏览器中访问 http://************ 测试系统"
echo "默认登录信息:"
echo "用户名: admin"
echo "密码: admin123"
