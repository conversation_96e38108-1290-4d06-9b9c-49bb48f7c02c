import axios from 'axios'

const API_URL = '/api'

// 登录
export const login = (username, password) => {
  return axios.post(`${API_URL}/users/login`, { username, password })
}

// 获取用户列表
export const getUsersList = () => {
  return axios.get(`${API_URL}/users/list`)
}

// 创建用户
export const createUser = (userData) => {
  return axios.post(`${API_URL}/users/`, userData)
}

// 更新用户
export const updateUser = (userId, userData) => {
  return axios.put(`${API_URL}/users/${userId}`, userData)
}

// 删除用户
export const deleteUser = (userId) => {
  return axios.delete(`${API_URL}/users/${userId}`)
} 