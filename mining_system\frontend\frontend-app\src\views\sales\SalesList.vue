<template>
  <div class="sales-container">
    <div class="page-header">
      <h2>销售记录列表</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="searchForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="客户">
        <el-input v-model="searchForm.customer" placeholder="请输入客户名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="sale_date" label="日期" width="120" />
      <el-table-column prop="customer.name" label="客户" width="180" />
      <el-table-column prop="vehicle.vehicle_id" label="车辆ID" width="120" />
      <el-table-column prop="time_loaded" label="装载时间" width="120">
        <template #default="{ row }">
          {{ formatTime(row.time_loaded) }}
        </template>
      </el-table-column>
      <el-table-column prop="product_type.name" label="产品" width="120" />
      <el-table-column prop="product_size" label="规格" width="120" />
      <el-table-column prop="gross_ton" label="毛重" width="100">
        <template #default="{ row }">
          {{ row.gross_ton }} 吨
        </template>
      </el-table-column>
      <el-table-column prop="tare_ton" label="皮重" width="100">
        <template #default="{ row }">
          {{ row.tare_ton }} 吨
        </template>
      </el-table-column>
      <el-table-column prop="net_ton" label="净重" width="100">
        <template #default="{ row }">
          {{ row.net_ton }} 吨
        </template>
      </el-table-column>
      <el-table-column prop="way_bill_no" label="运单号" min-width="150" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getSalesList } from '@/api/sales'
import { ElMessage } from 'element-plus'

// 搜索表单
const searchForm = ref({
  dateRange: [],
  customer: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      customer: searchForm.value.customer,
      start_date: searchForm.value.dateRange?.[0],
      end_date: searchForm.value.dateRange?.[1]
    }
    
    const response = await getSalesList(params)
    tableData.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return '-'
  return time.substring(11, 16) // 只显示时:分
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    dateRange: [],
    customer: ''
  }
  handleSearch()
}

// 刷新数据
const refreshData = () => {
  fetchData()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.sales-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 