一、项目背景
现状问题

目前您有一个 Excel 总表 来记录所有客户的每日销售数据，包括车队信息、装货时间、毛重皮重、净吨数、产品规格、运费及其他费用（部分手动录入）。

每个客户还有一个 独立表，用于查看该客户的历史进货量、付款情况等信息。

手动将 Excel 总表中的数据拆分给各家客户、再录入客户的独立表，会占用大量时间且容易出错。

客户付款后，您需要再手动去各自表中记录、更新欠款或结算信息。

需求目标

搭建一个 综合信息管理系统 (下文简称“系统”)，可以自动识别并导入每天的 Excel 数据到系统数据库，同时按规则分配到对应的客户记录。

当客户付款时，您只需在系统中手动录入付款信息，系统自动关联到该客户的应收账款并做相应更新。

系统可随时导出或打印单一客户的当期或历史汇总表，以供核对、交付或内部审计。

提供手动校正或修改数据的功能，例如更改某辆车的时间、重量信息、或运费等。

二、系统总体架构
数据源与数据流

数据源：Excel 总表(或CSV)

系统数据库：将原始数据、客户信息、付款记录、车队信息等存储至关系型数据库(如 MySQL/PostgreSQL) 或云端数据表。

前端界面：可 Web 化，也可做桌面应用；提供各业务操作入口与报表查看。

输出：提供按客户或时间范围等维度导出的 Excel/ PDF 报表。

主要模块

数据导入模块：识别 Excel 文件格式，解析每日销售数据；校验并写入系统数据库。

客户管理模块：查看/编辑客户资料；查看每个客户对应的销售与付款记录。

车队管理模块：系统可根据 Excel 中的“车队”或“Vehicle ID”字段进行识别和匹配，方便统计每辆车的拉料总吨数、运费等。

销售记录模块：展示、查询、筛选各项销售流水（含日期、车号、规格、重量、金额等）。

付款管理模块：手动录入并管理客户付款信息；自动更新应收账款、余额等。

报表模块：按客户/时间/规格等维度生成汇总报表；导出或打印。

权限与审计模块（可选）：若有多名员工使用系统，可区分角色与操作权限，并记录修改日志。

三、功能需求
以下列出了系统主要的功能性需求 (Functional Requirements)：

3.1 数据导入模块
上传 Excel/CSV 文件

用户可以在前端界面点击“导入数据”，选择当日或指定日期的 Excel/CSV 文件。

系统自动识别文件格式（列头、顺序、数据类型），若不匹配则提示用户进行列映射或出错警示。

数据解析与校验

解析每一行数据的必填字段：日期、客户名称、车辆ID、时间、规格、毛重、皮重、净重、Way Bill No.等。

对异常数据 (例如负数、缺失、格式不符) 做出警告或放入“异常列表”供人工修正。

写入数据库

将有效数据写入系统数据库，并与已有客户记录进行匹配：

若CUSTOMER存在于系统，自动关联；

若CUSTOMER为新客户，则自动创建或提示用户添加客户信息后再导入。

日志记录

每次导入完成后，系统生成导入日志(时间、操作者、文件名、成功/异常笔数)。

3.2 客户管理模块
新增/编辑客户信息

客户名称、联系人、联系方式、结算方式、信用额度等基本资料。

是否在系统内启用/停用该客户(若长期无业务可标记为历史客户)。

客户数据查看

列表：显示所有客户的基本资料、最近交易日期、累计欠款或预存款。

详情：某一客户的具体信息(历史交易、总购量、付款记录)。

3.3 车队管理模块
车辆信息维护

系统自动从导入的 Excel 获取 Vehicle ID、车队名称等信息；

或者管理员可手动录入、修改车辆信息(车牌号、车型、司机姓名等)。

车辆统计

查看某辆车/车队在某段时间内的总运量、总次数、平均净吨。

可汇总运费(若有运费记录)。

3.4 销售记录模块
查询与筛选

按时间范围、客户、规格、车辆ID、Way Bill No.等条件检索记录；

支持分页查看或导出筛选结果。

人工修改

在特殊情况下可手动修正某笔记录的重量、规格或客户归属；系统应保留修改痕迹或日志(修改者、修改时间、修改前后数值)。

统计汇总

系统可汇总显示查询结果的总毛重、总皮重、总净吨，以及对应的总金额(若后续存在单价或金额字段)。

3.5 付款管理模块
手动录入付款

选择客户 -> 输入付款日期、付款金额、付款方式(现金/转账等)、备注；

系统自动更新客户的应收账款余额。

查看应收与结余

每家客户都有一份收支表，展示累计采购金额、已付款金额、剩余欠款或预存款。

允许查询某时间段内的付款记录。

对账与冲销

当客户部分付款或预付时，可自动或手动选择冲抵某些发货单(若需要精细到发货单级别)。

系统保留付款/冲销的历史记录，便于审计。

3.6 报表模块
标准报表

客户月度采购报表：按客户、月份统计总车辆数、总净吨、总金额(若有)，以及各规格的分量；

车队运量报表：按车辆或车队统计某段时间的运量、平均载重；

应收账款报表：显示各客户的应收账款、已付金额、欠款结余等。

自定义查询并导出

用户根据筛选条件(时间、客户、车辆、规格)获取相应的数据后，一键导出为 Excel/PDF。

支持自定义表头或报表模板(可选需求)。

可视化分析 (可选)

使用图表/仪表盘呈现销售趋势、客户贡献度、收款进度等。   