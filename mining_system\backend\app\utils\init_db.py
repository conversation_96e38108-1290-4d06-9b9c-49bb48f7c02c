from sqlalchemy.orm import Session
from app.models.models import User, UserRole
import hashlib

def init_users(db: Session):
    """初始化用户数据"""
    # 检查是否已有管理员用户
    admin = db.query(User).filter(User.role == UserRole.ADMIN).first()
    
    # 如果没有管理员用户，创建默认管理员
    if not admin:
        admin = User(
            username="admin",
            password=hashlib.md5("admin123".encode()).hexdigest(),
            display_name="系统管理员",
            role=UserRole.ADMIN
        )
        db.add(admin)
        
        # 创建默认经理用户
        manager = User(
            username="manager",
            password=hashlib.md5("manager123".encode()).hexdigest(),
            display_name="部门经理",
            role=UserRole.MANAGER
        )
        db.add(manager)
        
        # 创建默认操作员用户
        operator = User(
            username="operator",
            password=hashlib.md5("operator123".encode()).hexdigest(),
            display_name="操作员",
            role=UserRole.OPERATOR
        )
        db.add(operator)
        
        db.commit()
        print("已创建默认用户账号")
        return True
    
    return False 