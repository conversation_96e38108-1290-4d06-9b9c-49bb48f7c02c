<template>
  <div class="file-uploader">
    <el-upload
      class="upload-area"
      drag
      action=""
      :http-request="uploadFile"
      :before-upload="beforeUpload"
      :show-file-list="false"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        拖拽文件到此处或 <em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          请上传Excel格式的每日销售报表文件(.xlsx, .xls)
        </div>
      </template>
    </el-upload>

    <div v-if="uploadStatus" class="upload-status">
      <el-alert
        :title="uploadStatus.message"
        :type="uploadStatus.type"
        :closable="false"
        show-icon
      />
      
      <div v-if="uploadResult" class="upload-result">
        <h3>导入结果</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="报表日期">{{ uploadResult.report_date }}</el-descriptions-item>
          <el-descriptions-item label="总记录数">{{ uploadResult.total_records }}</el-descriptions-item>
          <el-descriptions-item label="导入成功">{{ uploadResult.imported }}</el-descriptions-item>
          <el-descriptions-item label="跳过记录">{{ uploadResult.skipped }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="uploadResult.errors && uploadResult.errors.length" class="upload-errors">
          <h4>错误信息</h4>
          <el-collapse>
            <el-collapse-item title="查看详情" name="errors">
              <ul>
                <li v-for="(error, index) in uploadResult.errors" :key="index">
                  {{ error }}
                </li>
              </ul>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
import { uploadDailySalesReport } from '@/api/import'
import { ElMessage } from 'element-plus'

const uploadStatus = ref(null)
const uploadResult = ref(null)

// 上传前验证
const beforeUpload = (file) => {
  const isExcel = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  
  uploadStatus.value = {
    type: 'info',
    message: '正在上传文件...'
  }
  
  return true
}

// 自定义上传方法
const uploadFile = async (options) => {
  try {
    const formData = new FormData()
    formData.append('file', options.file)
    
    uploadStatus.value = {
      type: 'info',
      message: '正在处理数据...'
    }
    
    const res = await uploadDailySalesReport(formData)
    
    uploadResult.value = res
    uploadStatus.value = {
      type: 'success',
      message: '文件导入成功!'
    }
  } catch (error) {
    uploadStatus.value = {
      type: 'error',
      message: `导入失败: ${error.message || '未知错误'}`
    }
    uploadResult.value = null
  }
}
</script>

<style scoped>
.file-uploader {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.upload-area {
  margin-bottom: 20px;
  width: 100%;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px dashed #c0c4cc;
  border-radius: 8px;
  background-color: rgba(64, 158, 255, 0.05);
  transition: all 0.3s;
}

.upload-area :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.upload-area :deep(.el-icon--upload) {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 15px;
}

.upload-area :deep(.el-upload__text) {
  font-size: 16px;
  color: #606266;
}

.upload-area :deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
  font-weight: 600;
}

.upload-area :deep(.el-upload__tip) {
  font-size: 14px;
  margin-top: 15px;
  color: #909399;
}

.upload-status {
  margin-top: 20px;
}

.upload-result {
  margin-top: 15px;
  padding: 15px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.upload-result h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.upload-errors {
  margin-top: 20px;
}

.upload-errors h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #f56c6c;
  font-size: 16px;
  font-weight: 600;
}

.upload-errors ul {
  margin: 0;
  padding: 0 0 0 20px;
}

.upload-errors li {
  margin-bottom: 8px;
  color: #f56c6c;
}
</style> 