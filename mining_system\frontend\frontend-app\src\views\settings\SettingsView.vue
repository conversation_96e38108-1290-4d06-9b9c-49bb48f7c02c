<template>
  <div class="settings-container">
    <div class="page-header">
      <h2>系统设置</h2>
    </div>

    <el-tabs v-model="activeTab">
      <!-- 基本设置 -->
      <el-tab-pane label="基本设置" name="basic">
        <div class="settings-form-container">
          <el-form 
            v-loading="loading" 
            :model="formData" 
            label-width="120px"
          >
            <div v-for="setting in basicSettings" :key="setting.key">
              <el-form-item :label="setting.description">
                <el-input v-model="formData[setting.key]" />
              </el-form-item>
            </div>

            <el-form-item>
              <el-button type="primary" @click="saveSettings('basic')">保存设置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 数据备份 -->
      <el-tab-pane label="数据备份" name="backup">
        <div class="backup-actions">
          <el-button 
            type="primary" 
            @click="handleBackup"
            :loading="backupLoading"
          >
            创建备份
          </el-button>
          <el-button 
            type="primary" 
            @click="fetchBackups"
          >
            刷新列表
          </el-button>
        </div>

        <el-table
          v-loading="backupsListLoading"
          :data="backupsList"
          border
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column prop="filename" label="备份文件" min-width="180" />
          <el-table-column prop="created_at" label="创建时间" min-width="180" />
          <el-table-column prop="size_kb" label="文件大小">
            <template #default="{ row }">
              {{ row.size_kb }} KB
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-download"
                @click="downloadBackup(row)"
              >
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="backup-note" v-if="backupsList.length > 0">
          <p>备份存储路径: {{ backupPath }}</p>
        </div>
      </el-tab-pane>

      <!-- 系统信息 -->
      <el-tab-pane label="系统信息" name="info">
        <div v-loading="statsLoading">
          <el-descriptions title="数据库统计" :column="1" border>
            <el-descriptions-item label="数据库大小">
              {{ stats.database_size_kb }} KB
            </el-descriptions-item>
            <el-descriptions-item label="客户数量">
              {{ stats.tables?.customers || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="车辆数量">
              {{ stats.tables?.vehicles || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="产品类型数量">
              {{ stats.tables?.product_types || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="销售记录数量">
              {{ stats.tables?.sales_records || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="用户数量">
              {{ stats.tables?.users || 0 }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="danger-zone">
            <h3>危险操作区</h3>
            <el-alert
              title="以下操作将会永久删除数据，请谨慎操作！"
              type="error"
              :closable="false"
              show-icon
            />
            <div class="danger-actions">
              <el-popconfirm
                title="确定要重置数据库吗？此操作将会删除所有数据且无法恢复！"
                @confirm="handleReset"
              >
                <template #reference>
                  <el-button 
                    type="danger" 
                    :loading="resetLoading"
                  >
                    重置数据库
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAllSettings,
  updateSetting,
  createBackup,
  getBackupsList,
  resetDatabase,
  getDatabaseStats
} from '@/api/settings'
import authStore from '@/store/auth'

// 检查是否有管理员权限
const isAdmin = computed(() => {
  return authStore.userRole.value === 'admin'
})

// 当前激活的标签页
const activeTab = ref('basic')

// 基本设置相关
const loading = ref(false)
const formData = reactive({})
const settings = ref([])
const basicSettings = computed(() => {
  return settings.value.filter(setting => 
    ['company_name', 'date_format', 'backup_path'].includes(setting.key)
  )
})

// 备份相关
const backupLoading = ref(false)
const backupsListLoading = ref(false)
const backupsList = ref([])
const backupPath = ref('')

// 系统信息相关
const statsLoading = ref(false)
const resetLoading = ref(false)
const stats = ref({
  database_size_kb: 0,
  tables: {
    customers: 0,
    vehicles: 0,
    product_types: 0,
    sales_records: 0,
    users: 0
  }
})

// 获取所有设置
const fetchSettings = async () => {
  loading.value = true
  try {
    const response = await getAllSettings()
    settings.value = response.data
    
    // 设置表单数据
    settings.value.forEach(setting => {
      formData[setting.key] = setting.value
    })
  } catch (error) {
    ElMessage.error('获取设置失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 保存设置
const saveSettings = async (section) => {
  loading.value = true
  try {
    let settingsToUpdate = []
    
    if (section === 'basic') {
      settingsToUpdate = basicSettings.value
    }
    
    for (const setting of settingsToUpdate) {
      if (formData[setting.key] !== setting.value) {
        await updateSetting(setting.key, {
          value: formData[setting.key],
          description: setting.description
        })
      }
    }
    
    ElMessage.success('设置已保存')
    await fetchSettings()
  } catch (error) {
    ElMessage.error('保存设置失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 创建备份
const handleBackup = async () => {
  backupLoading.value = true
  try {
    const response = await createBackup()
    ElMessage.success('备份已创建: ' + response.data.backup_file)
    fetchBackups()
  } catch (error) {
    ElMessage.error('创建备份失败：' + error.message)
  } finally {
    backupLoading.value = false
  }
}

// 获取备份列表
const fetchBackups = async () => {
  backupsListLoading.value = true
  try {
    const response = await getBackupsList()
    backupsList.value = response.data.backup_files
    backupPath.value = response.data.backup_path
  } catch (error) {
    ElMessage.error('获取备份列表失败：' + error.message)
  } finally {
    backupsListLoading.value = false
  }
}

// 下载备份
const downloadBackup = (backup) => {
  ElMessage.info('备份文件位置: ' + backup.path)
  ElMessage.info('请使用文件管理器从此路径获取备份文件')
}

// 获取数据库统计信息
const fetchDatabaseStats = async () => {
  statsLoading.value = true
  try {
    const response = await getDatabaseStats()
    stats.value = response.data
  } catch (error) {
    ElMessage.error('获取数据库统计信息失败：' + error.message)
  } finally {
    statsLoading.value = false
  }
}

// 重置数据库
const handleReset = async () => {
  // 只允许管理员操作
  if (!isAdmin.value) {
    ElMessage.error('只有管理员才能执行此操作')
    return
  }
  
  resetLoading.value = true
  try {
    await resetDatabase()
    ElMessage.success('数据库已重置')
    fetchDatabaseStats()
  } catch (error) {
    ElMessage.error('重置数据库失败：' + error.message)
  } finally {
    resetLoading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchSettings()
  fetchBackups()
  fetchDatabaseStats()
})
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.settings-form-container {
  max-width: 600px;
  margin-top: 20px;
}

.backup-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.backup-note {
  margin-top: 20px;
  font-size: 14px;
  color: #606266;
}

.danger-zone {
  margin-top: 40px;
  padding: 20px;
  border: 1px solid #f56c6c;
  border-radius: 4px;
}

.danger-zone h3 {
  color: #f56c6c;
  margin-top: 0;
  margin-bottom: 15px;
}

.danger-actions {
  margin-top: 20px;
}
</style> 