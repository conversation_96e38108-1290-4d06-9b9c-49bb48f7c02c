from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List
from datetime import datetime
from app.db.database import get_db
from app.models.models import ProductType, SalesRecord
from pydantic import BaseModel

router = APIRouter(
    prefix="/products",
    tags=["products"],
)

# Pydantic模型
class ProductTypeBase(BaseModel):
    name: str
    description: Optional[str] = None

class ProductTypeCreate(ProductTypeBase):
    pass

class ProductTypeUpdate(ProductTypeBase):
    pass

class ProductTypeResponse(ProductTypeBase):
    id: int
    
    class Config:
        orm_mode = True

# API路由
@router.get("/list", response_model=List[ProductTypeResponse])
async def get_product_types_list(
    db: Session = Depends(get_db),
    search: Optional[str] = None
):
    """获取产品类型列表"""
    query = db.query(ProductType)
    
    if search:
        query = query.filter(ProductType.name.ilike(f"%{search}%"))
    
    product_types = query.order_by(ProductType.name).all()
    return product_types

@router.post("/", response_model=ProductTypeResponse)
async def create_product_type(
    product_type: ProductTypeCreate,
    db: Session = Depends(get_db)
):
    """创建新产品类型"""
    # 检查名称是否已存在
    existing = db.query(ProductType).filter(ProductType.name == product_type.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="产品类型名称已存在")
    
    db_product_type = ProductType(**product_type.dict())
    db.add(db_product_type)
    db.commit()
    db.refresh(db_product_type)
    return db_product_type

@router.put("/{product_type_id}", response_model=ProductTypeResponse)
async def update_product_type(
    product_type_id: int,
    product_type: ProductTypeUpdate,
    db: Session = Depends(get_db)
):
    """更新产品类型信息"""
    db_product_type = db.query(ProductType).filter(ProductType.id == product_type_id).first()
    if not db_product_type:
        raise HTTPException(status_code=404, detail="产品类型不存在")
    
    # 检查新名称是否与其他产品类型重复
    if product_type.name != db_product_type.name:
        existing = db.query(ProductType).filter(ProductType.name == product_type.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="产品类型名称已存在")
    
    for key, value in product_type.dict().items():
        setattr(db_product_type, key, value)
    
    db.commit()
    db.refresh(db_product_type)
    return db_product_type

@router.delete("/{product_type_id}")
async def delete_product_type(
    product_type_id: int,
    db: Session = Depends(get_db)
):
    """删除产品类型"""
    # 检查是否存在关联的销售记录
    sales_count = db.query(SalesRecord)\
        .filter(SalesRecord.product_type_id == product_type_id)\
        .count()
    
    if sales_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除该产品类型，存在{sales_count}条关联的销售记录"
        )
    
    db_product_type = db.query(ProductType).filter(ProductType.id == product_type_id).first()
    if not db_product_type:
        raise HTTPException(status_code=404, detail="产品类型不存在")
    
    db.delete(db_product_type)
    db.commit()
    return {"message": "产品类型删除成功"}

@router.get("/{product_type_id}/statistics")
async def get_product_type_statistics(
    product_type_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db)
):
    """获取产品类型统计信息"""
    # 检查产品类型是否存在
    product_type = db.query(ProductType).filter(ProductType.id == product_type_id).first()
    if not product_type:
        raise HTTPException(status_code=404, detail="产品类型不存在")
    
    # 构建查询
    query = db.query(
        func.count(SalesRecord.id).label('total_orders'),
        func.sum(SalesRecord.net_ton).label('total_weight'),
        func.avg(SalesRecord.net_ton).label('avg_weight_per_order')
    ).filter(SalesRecord.product_type_id == product_type_id)
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    stats = query.first()
    
    # 获取购买该产品的客户数量
    customer_count = db.query(func.count(func.distinct(SalesRecord.customer_id)))\
        .filter(SalesRecord.product_type_id == product_type_id)\
        .scalar() or 0
    
    return {
        "product_name": product_type.name,
        "description": product_type.description,
        "total_orders": stats.total_orders or 0,
        "total_weight": float(stats.total_weight or 0),
        "avg_weight_per_order": float(stats.avg_weight_per_order or 0),
        "customer_count": customer_count,
        "start_date": start_date,
        "end_date": end_date
    } 