import axios from 'axios'

const API_URL = '/api'

// 获取车辆列表
export const getVehiclesList = (params) => {
  return axios.get(`${API_URL}/vehicles/list`, { params })
}

// 创建新车辆
export const createVehicle = (data) => {
  return axios.post(`${API_URL}/vehicles/`, data)
}

// 更新车辆信息
export const updateVehicle = (id, data) => {
  return axios.put(`${API_URL}/vehicles/${id}`, data)
}

// 删除车辆
export const deleteVehicle = (id) => {
  return axios.delete(`${API_URL}/vehicles/${id}`)
}

// 获取车辆统计信息
export const getVehicleStatistics = (id, params) => {
  return axios.get(`${API_URL}/vehicles/${id}/statistics`, { params })
} 