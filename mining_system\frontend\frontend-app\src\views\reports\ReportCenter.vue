<template>
  <div class="report-container">
    <div class="page-header">
      <h2>报表中心</h2>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
        />
        <el-button type="primary" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 销售汇总 -->
    <el-card class="report-card">
      <template #header>
        <div class="card-header">
          <span>销售汇总</span>
        </div>
      </template>
      <div v-loading="loading.sales">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-title">总订单数</div>
              <div class="stat-value">{{ salesSummary.totals?.total_orders || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-title">总销售重量</div>
              <div class="stat-value">{{ formatWeight(salesSummary.totals?.total_weight) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-title">日均订单数</div>
              <div class="stat-value">{{ formatNumber(salesSummary.totals?.avg_daily_orders, 1) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-title">日均销售重量</div>
              <div class="stat-value">{{ formatWeight(salesSummary.totals?.avg_daily_weight) }}</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="chart-container">
          <el-table :data="salesSummary.daily_stats" stripe style="width: 100%">
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="order_count" label="订单数" width="100" />
            <el-table-column prop="total_weight" label="销售重量">
              <template #default="{ row }">
                {{ formatWeight(row.total_weight) }}
              </template>
            </el-table-column>
            <el-table-column prop="customer_count" label="客户数" width="100" />
            <el-table-column prop="vehicle_count" label="车辆数" width="100" />
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 客户分析 -->
    <el-card class="report-card">
      <template #header>
        <div class="card-header">
          <span>客户分析</span>
          <el-input-number
            v-model="customerTopN"
            :min="5"
            :max="100"
            size="small"
            @change="handleCustomerTopNChange"
          >
            <template #prefix>显示前</template>
            <template #suffix>名</template>
          </el-input-number>
        </div>
      </template>
      <div v-loading="loading.customer">
        <el-row :gutter="20" class="stat-summary">
          <el-col :span="12">
            <div class="stat-box">
              <div class="stat-title">30天活跃客户数</div>
              <div class="stat-value">{{ customerAnalysis.statistics?.active_customers_30d || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-box">
              <div class="stat-title">总客户数</div>
              <div class="stat-value">{{ customerAnalysis.statistics?.total_customers || 0 }}</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="chart-container">
          <el-table :data="customerAnalysis.top_customers" stripe style="width: 100%">
            <el-table-column prop="customer_name" label="客户名称" min-width="150" />
            <el-table-column prop="order_count" label="订单数" width="100" />
            <el-table-column prop="total_weight" label="总重量">
              <template #default="{ row }">
                {{ formatWeight(row.total_weight) }}
              </template>
            </el-table-column>
            <el-table-column prop="avg_weight_per_order" label="平均每单重量">
              <template #default="{ row }">
                {{ formatWeight(row.avg_weight_per_order) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 车辆运营分析 -->
    <el-card class="report-card">
      <template #header>
        <div class="card-header">
          <span>车辆运营分析</span>
          <el-input-number
            v-model="vehicleTopN"
            :min="5"
            :max="100"
            size="small"
            @change="handleVehicleTopNChange"
          >
            <template #prefix>显示前</template>
            <template #suffix>名</template>
          </el-input-number>
        </div>
      </template>
      <div v-loading="loading.vehicle">
        <el-row :gutter="20" class="stat-summary">
          <el-col :span="12">
            <div class="stat-box">
              <div class="stat-title">30天活跃车辆数</div>
              <div class="stat-value">{{ vehicleAnalysis.statistics?.active_vehicles_30d || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-box">
              <div class="stat-title">总车辆数</div>
              <div class="stat-value">{{ vehicleAnalysis.statistics?.total_vehicles || 0 }}</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="chart-container">
          <el-table :data="vehicleAnalysis.top_vehicles" stripe style="width: 100%">
            <el-table-column prop="plate_number" label="车牌号" min-width="120" />
            <el-table-column prop="driver_name" label="司机姓名" width="120" />
            <el-table-column prop="delivery_count" label="运输次数" width="100" />
            <el-table-column prop="total_weight" label="总运输重量">
              <template #default="{ row }">
                {{ formatWeight(row.total_weight) }}
              </template>
            </el-table-column>
            <el-table-column prop="avg_weight_per_delivery" label="平均每次重量">
              <template #default="{ row }">
                {{ formatWeight(row.avg_weight_per_delivery) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 产品销售分析 -->
    <el-card class="report-card">
      <template #header>
        <div class="card-header">
          <span>产品销售分析</span>
        </div>
      </template>
      <div v-loading="loading.product">
        <div class="chart-container">
          <el-table :data="productAnalysis.product_stats" stripe style="width: 100%">
            <el-table-column prop="product_size" label="产品规格" min-width="120" />
            <el-table-column prop="order_count" label="订单数" width="100" />
            <el-table-column prop="total_weight" label="总销售重量">
              <template #default="{ row }">
                {{ formatWeight(row.total_weight) }}
              </template>
            </el-table-column>
            <el-table-column prop="avg_weight_per_order" label="平均每单重量">
              <template #default="{ row }">
                {{ formatWeight(row.avg_weight_per_order) }}
              </template>
            </el-table-column>
            <el-table-column prop="weight_percentage" label="重量占比">
              <template #default="{ row }">
                {{ row.weight_percentage }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getSalesSummary,
  getCustomerAnalysis,
  getVehicleAnalysis,
  getProductAnalysis
} from '@/api/reports'

// 日期范围
const dateRange = ref([])

// 显示数量控制
const customerTopN = ref(10)
const vehicleTopN = ref(10)

// 加载状态
const loading = ref({
  sales: false,
  customer: false,
  vehicle: false,
  product: false
})

// 数据存储
const salesSummary = ref({
  daily_stats: [],
  totals: {}
})
const customerAnalysis = ref({
  top_customers: [],
  statistics: {}
})
const vehicleAnalysis = ref({
  top_vehicles: [],
  statistics: {}
})
const productAnalysis = ref({
  product_stats: [],
  total_weight: 0
})

// 格式化数字
const formatNumber = (num, decimals = 0) => {
  if (num === undefined || num === null) return '-'
  return Number(num).toFixed(decimals)
}

// 格式化重量
const formatWeight = (weight) => {
  if (weight === undefined || weight === null) return '-'
  return `${Number(weight).toFixed(2)}吨`
}

// 获取数据
const fetchSalesSummary = async () => {
  loading.value.sales = true
  try {
    const params = {}
    if (dateRange.value?.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    const response = await getSalesSummary(params)
    salesSummary.value = response.data
  } catch (error) {
    ElMessage.error('获取销售汇总数据失败：' + error.message)
  } finally {
    loading.value.sales = false
  }
}

const fetchCustomerAnalysis = async () => {
  loading.value.customer = true
  try {
    const params = {
      top_n: customerTopN.value
    }
    if (dateRange.value?.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    const response = await getCustomerAnalysis(params)
    customerAnalysis.value = response.data
  } catch (error) {
    ElMessage.error('获取客户分析数据失败：' + error.message)
  } finally {
    loading.value.customer = false
  }
}

const fetchVehicleAnalysis = async () => {
  loading.value.vehicle = true
  try {
    const params = {
      top_n: vehicleTopN.value
    }
    if (dateRange.value?.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    const response = await getVehicleAnalysis(params)
    vehicleAnalysis.value = response.data
  } catch (error) {
    ElMessage.error('获取车辆运营数据失败：' + error.message)
  } finally {
    loading.value.vehicle = false
  }
}

const fetchProductAnalysis = async () => {
  loading.value.product = true
  try {
    const params = {}
    if (dateRange.value?.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    const response = await getProductAnalysis(params)
    productAnalysis.value = response.data
  } catch (error) {
    ElMessage.error('获取产品销售数据失败：' + error.message)
  } finally {
    loading.value.product = false
  }
}

// 刷新所有数据
const refreshData = () => {
  fetchSalesSummary()
  fetchCustomerAnalysis()
  fetchVehicleAnalysis()
  fetchProductAnalysis()
}

// 事件处理
const handleDateRangeChange = () => {
  refreshData()
}

const handleCustomerTopNChange = () => {
  fetchCustomerAnalysis()
}

const handleVehicleTopNChange = () => {
  fetchVehicleAnalysis()
}

// 页面加载时获取数据
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.report-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.report-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-summary {
  margin-bottom: 20px;
}

.stat-box {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.stat-title {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
}

.stat-value {
  color: #303133;
  font-size: 24px;
  font-weight: bold;
}

.chart-container {
  margin-top: 20px;
}
</style> 