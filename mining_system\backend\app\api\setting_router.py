from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.db.database import get_db, engine, Base
from app.models.models import SystemSetting
from pydantic import BaseModel
import os
import shutil
import sqlite3
import time

router = APIRouter(
    prefix="/settings",
    tags=["settings"],
)

# Pydantic模型
class SettingBase(BaseModel):
    key: str
    value: str
    description: Optional[str] = None

class SettingCreate(SettingBase):
    pass

class SettingUpdate(BaseModel):
    value: str
    description: Optional[str] = None

class SettingResponse(SettingBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

# 默认系统设置
DEFAULT_SETTINGS = [
    {
        "key": "company_name", 
        "value": "矿山石料销售公司",
        "description": "公司名称"
    },
    {
        "key": "backup_path", 
        "value": "./backups",
        "description": "数据备份存储路径"
    },
    {
        "key": "autobackup_enabled", 
        "value": "false",
        "description": "是否启用自动备份"
    },
    {
        "key": "date_format", 
        "value": "YYYY-MM-DD",
        "description": "日期格式"
    }
]

# 工具函数
def init_settings(db: Session):
    """初始化系统设置"""
    for setting in DEFAULT_SETTINGS:
        existing = db.query(SystemSetting).filter(SystemSetting.key == setting["key"]).first()
        if not existing:
            new_setting = SystemSetting(**setting)
            db.add(new_setting)
    db.commit()

def backup_database(backup_path: str):
    """备份数据库"""
    # 确保备份目录存在
    os.makedirs(backup_path, exist_ok=True)
    
    # 创建带时间戳的备份文件名
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    backup_file = os.path.join(backup_path, f"backup_{timestamp}.db")
    
    # 连接数据库，创建备份
    conn = sqlite3.connect('app.db')
    backup = sqlite3.connect(backup_file)
    conn.backup(backup)
    backup.close()
    conn.close()
    
    return backup_file

# API路由
@router.get("/", response_model=List[SettingResponse])
async def get_all_settings(db: Session = Depends(get_db)):
    """获取所有系统设置"""
    # 确保系统设置已初始化
    init_settings(db)
    
    settings = db.query(SystemSetting).all()
    return settings

@router.get("/stats")
async def get_database_stats(db: Session = Depends(get_db)):
    """获取数据库统计信息"""
    try:
        # 获取各表的记录数
        customers_count = db.execute("SELECT COUNT(*) FROM customers").scalar()
        vehicles_count = db.execute("SELECT COUNT(*) FROM vehicles").scalar()
        product_types_count = db.execute("SELECT COUNT(*) FROM product_types").scalar()
        sales_records_count = db.execute("SELECT COUNT(*) FROM sales_records").scalar()
        users_count = db.execute("SELECT COUNT(*) FROM users").scalar()
        
        # 获取数据库文件大小
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "app.db")
        
        # 检查文件是否存在
        if os.path.exists(db_path):
            db_size_kb = round(os.path.getsize(db_path) / 1024, 2)
        else:
            # 尝试相对路径
            if os.path.exists("app.db"):
                db_size_kb = round(os.path.getsize("app.db") / 1024, 2)
            else:
                db_size_kb = 0
        
        return {
            "tables": {
                "customers": customers_count,
                "vehicles": vehicles_count,
                "product_types": product_types_count,
                "sales_records": sales_records_count,
                "users": users_count
            },
            "database_size_kb": db_size_kb
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库统计信息失败: {str(e)}")

@router.get("/{key}", response_model=SettingResponse)
async def get_setting(key: str, db: Session = Depends(get_db)):
    """获取指定键的系统设置"""
    setting = db.query(SystemSetting).filter(SystemSetting.key == key).first()
    if not setting:
        raise HTTPException(status_code=404, detail="设置不存在")
    return setting

@router.put("/{key}", response_model=SettingResponse)
async def update_setting(
    key: str,
    setting: SettingUpdate,
    db: Session = Depends(get_db)
):
    """更新系统设置"""
    db_setting = db.query(SystemSetting).filter(SystemSetting.key == key).first()
    if not db_setting:
        raise HTTPException(status_code=404, detail="设置不存在")
    
    db_setting.value = setting.value
    if setting.description:
        db_setting.description = setting.description
    
    db.commit()
    db.refresh(db_setting)
    return db_setting

@router.post("/backup")
async def create_backup(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """创建数据库备份"""
    # 获取备份路径设置
    backup_path_setting = db.query(SystemSetting).filter(SystemSetting.key == "backup_path").first()
    if not backup_path_setting:
        # 使用默认路径
        backup_path = "./backups"
    else:
        backup_path = backup_path_setting.value
    
    # 在后台任务中执行备份
    try:
        backup_file = backup_database(backup_path)
        return {
            "success": True,
            "message": "备份已创建",
            "backup_file": backup_file
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"备份失败: {str(e)}")

@router.get("/backup/list")
async def list_backups(db: Session = Depends(get_db)):
    """列出所有备份"""
    # 获取备份路径设置
    backup_path_setting = db.query(SystemSetting).filter(SystemSetting.key == "backup_path").first()
    if not backup_path_setting:
        backup_path = "./backups"
    else:
        backup_path = backup_path_setting.value
    
    # 确保备份目录存在
    os.makedirs(backup_path, exist_ok=True)
    
    try:
        # 获取备份文件列表
        backup_files = []
        for file in os.listdir(backup_path):
            if file.startswith("backup_") and file.endswith(".db"):
                file_path = os.path.join(backup_path, file)
                file_stat = os.stat(file_path)
                backup_files.append({
                    "filename": file,
                    "path": file_path,
                    "size_kb": round(file_stat.st_size / 1024, 2),
                    "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat()
                })
        
        # 按创建时间倒序排序
        backup_files.sort(key=lambda x: x["created_at"], reverse=True)
        
        return {
            "backup_path": backup_path,
            "backup_files": backup_files
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取备份列表失败: {str(e)}")

@router.post("/reset")
async def reset_database(db: Session = Depends(get_db)):
    """重置数据库到初始状态（慎用）"""
    try:
        # 删除所有表
        Base.metadata.drop_all(bind=engine)
        
        # 重新创建表
        Base.metadata.create_all(bind=engine)
        
        # 重新初始化设置
        init_settings(db)
        
        return {
            "success": True,
            "message": "数据库已重置到初始状态"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置数据库失败: {str(e)}") 