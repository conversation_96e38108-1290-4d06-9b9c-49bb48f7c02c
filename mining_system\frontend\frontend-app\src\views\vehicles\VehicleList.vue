<template>
  <div class="vehicle-container">
    <div class="page-header">
      <h2>车辆管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">新增车辆</el-button>
        <el-button type="primary" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="搜索">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入车牌号或司机姓名"
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="plate_number" label="车牌号" min-width="120" />
      <el-table-column prop="driver_name" label="司机姓名" width="120" />
      <el-table-column prop="driver_contact" label="联系电话" width="120" />
      <el-table-column prop="vehicle_type" label="车辆类型" width="120" />
      <el-table-column prop="max_load" label="最大载重" width="120">
        <template #default="{ row }">
          {{ row.max_load ? `${row.max_load}吨` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="notes" label="备注" min-width="200" />
      <el-table-column prop="created_at" label="创建时间" width="120">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleViewStats(row)"
          >
            统计
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 车辆表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增车辆' : '编辑车辆'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="车牌号" prop="plate_number">
          <el-input v-model="form.plate_number" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="司机姓名" prop="driver_name">
          <el-input v-model="form.driver_name" placeholder="请输入司机姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="driver_contact">
          <el-input v-model="form.driver_contact" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="车辆类型" prop="vehicle_type">
          <el-select v-model="form.vehicle_type" placeholder="请选择车辆类型" clearable>
            <el-option label="重型货车" value="重型货车" />
            <el-option label="中型货车" value="中型货车" />
            <el-option label="轻型货车" value="轻型货车" />
          </el-select>
        </el-form-item>
        <el-form-item label="最大载重" prop="max_load">
          <el-input-number
            v-model="form.max_load"
            :min="0"
            :precision="2"
            :step="0.5"
            placeholder="请输入最大载重"
          >
            <template #suffix>吨</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog
      v-model="statsDialogVisible"
      title="车辆统计信息"
      width="600px"
    >
      <el-form label-width="140px">
        <el-form-item label="统计时间范围">
          <el-date-picker
            v-model="statsDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleStatsDateChange"
          />
        </el-form-item>
      </el-form>
      
      <el-descriptions :column="1" border>
        <el-descriptions-item label="车牌号">
          {{ statsData.plate_number }}
        </el-descriptions-item>
        <el-descriptions-item label="司机姓名">
          {{ statsData.driver_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="总运输次数">
          {{ statsData.total_deliveries }} 次
        </el-descriptions-item>
        <el-descriptions-item label="总运输重量">
          {{ statsData.total_weight?.toFixed(2) }} 吨
        </el-descriptions-item>
        <el-descriptions-item label="平均每次重量">
          {{ statsData.avg_weight_per_delivery?.toFixed(2) }} 吨
        </el-descriptions-item>
        <el-descriptions-item label="服务客户数">
          {{ statsData.customer_count }} 个
        </el-descriptions-item>
        <el-descriptions-item label="最常运送产品">
          {{ statsData.most_delivered_product || '暂无数据' }}
        </el-descriptions-item>
        <el-descriptions-item label="最常服务客户">
          {{ statsData.top_customer || '暂无数据' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getVehiclesList,
  createVehicle,
  updateVehicle,
  deleteVehicle,
  getVehicleStatistics
} from '@/api/vehicles'

// 搜索表单
const searchForm = ref({
  search: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = ref({
  plate_number: '',
  driver_name: '',
  driver_contact: '',
  vehicle_type: '',
  max_load: null,
  notes: ''
})

// 表单验证规则
const rules = {
  plate_number: [
    { required: true, message: '请输入车牌号', trigger: 'blur' },
    { min: 5, max: 10, message: '长度在 5 到 10 个字符', trigger: 'blur' }
  ],
  driver_name: [
    { required: true, message: '请输入司机姓名', trigger: 'blur' }
  ],
  driver_contact: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 统计信息
const statsDialogVisible = ref(false)
const statsDateRange = ref([])
const statsData = ref({})
const currentVehicleId = ref(null)

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return date.split('T')[0]
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchForm.value.search
    }
    const response = await getVehiclesList(params)
    tableData.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value.search = ''
  handleSearch()
}

// 刷新数据
const refreshData = () => {
  fetchData()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 新增车辆
const handleAdd = () => {
  dialogType.value = 'add'
  form.value = {
    plate_number: '',
    driver_name: '',
    driver_contact: '',
    vehicle_type: '',
    max_load: null,
    notes: ''
  }
  dialogVisible.value = true
}

// 编辑车辆
const handleEdit = (row) => {
  dialogType.value = 'edit'
  form.value = { ...row }
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await createVehicle(form.value)
          ElMessage.success('创建成功')
        } else {
          await updateVehicle(form.value.id, form.value)
          ElMessage.success('更新成功')
        }
        dialogVisible.value = false
        fetchData()
      } catch (error) {
        ElMessage.error('操作失败：' + error.response?.data?.detail || error.message)
      }
    }
  })
}

// 删除车辆
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除该车辆吗？删除后无法恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteVehicle(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      ElMessage.error('删除失败：' + error.response?.data?.detail || error.message)
    }
  })
}

// 查看统计信息
const handleViewStats = async (row) => {
  currentVehicleId.value = row.id
  statsDialogVisible.value = true
  statsDateRange.value = []
  await fetchVehicleStats()
}

// 获取车辆统计信息
const fetchVehicleStats = async () => {
  if (!currentVehicleId.value) return
  
  try {
    const params = {}
    if (statsDateRange.value?.length === 2) {
      params.start_date = statsDateRange.value[0]
      params.end_date = statsDateRange.value[1]
    }
    
    const response = await getVehicleStatistics(currentVehicleId.value, params)
    statsData.value = response.data
  } catch (error) {
    ElMessage.error('获取统计信息失败：' + error.message)
  }
}

// 统计日期范围变化
const handleStatsDateChange = () => {
  fetchVehicleStats()
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.vehicle-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 