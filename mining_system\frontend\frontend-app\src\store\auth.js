import { ref, reactive } from 'vue'

// 创建响应式状态
const currentUser = ref(null)
const isAuthenticated = ref(false)
const userRole = ref(null)

// 从localStorage恢复用户会话
const initUserState = () => {
  const savedUser = localStorage.getItem('currentUser')
  if (savedUser) {
    try {
      const user = JSON.parse(savedUser)
      currentUser.value = user
      isAuthenticated.value = true
      userRole.value = user.role
    } catch (error) {
      console.error('Failed to parse saved user:', error)
      localStorage.removeItem('currentUser')
    }
  }
}

// 设置当前用户
const setCurrentUser = (user) => {
  currentUser.value = user
  isAuthenticated.value = !!user
  userRole.value = user?.role || null
  
  if (user) {
    localStorage.setItem('currentUser', JSON.stringify(user))
  } else {
    localStorage.removeItem('currentUser')
  }
}

// 登出
const logout = () => {
  setCurrentUser(null)
}

// 在应用启动时初始化用户状态
initUserState()

export default {
  currentUser,
  isAuthenticated,
  userRole,
  setCurrentUser,
  logout
} 