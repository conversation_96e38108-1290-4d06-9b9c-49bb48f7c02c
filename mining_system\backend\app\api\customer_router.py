from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List
from datetime import date, datetime
from app.db.database import get_db
from app.models.models import Customer, SalesRecord
from pydantic import BaseModel

router = APIRouter(
    prefix="/customers",
    tags=["customers"],
)

class CustomerBase(BaseModel):
    name: str
    contact: Optional[str] = None
    address: Optional[str] = None

class CustomerCreate(CustomerBase):
    pass

class CustomerUpdate(CustomerBase):
    pass

class CustomerResponse(CustomerBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

@router.get("/list")
async def get_customers_list(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = None
):
    """获取客户列表"""
    query = db.query(Customer)
    
    if search:
        query = query.filter(Customer.name.ilike(f"%{search}%"))
    
    total = query.count()
    
    customers = query.order_by(Customer.name)\
        .offset((page - 1) * page_size)\
        .limit(page_size)\
        .all()
    
    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "items": customers
    }

@router.post("/", response_model=CustomerResponse)
async def create_customer(
    customer: CustomerCreate,
    db: Session = Depends(get_db)
):
    """创建新客户"""
    # 检查客户名是否已存在
    existing = db.query(Customer).filter(Customer.name == customer.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="客户名称已存在")
    
    db_customer = Customer(**customer.dict())
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)
    return db_customer

@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: int,
    customer: CustomerUpdate,
    db: Session = Depends(get_db)
):
    """更新客户信息"""
    db_customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not db_customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 检查新名称是否与其他客户重复
    if customer.name != db_customer.name:
        existing = db.query(Customer).filter(Customer.name == customer.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="客户名称已存在")
    
    for key, value in customer.dict().items():
        setattr(db_customer, key, value)
    
    db.commit()
    db.refresh(db_customer)
    return db_customer

@router.delete("/{customer_id}")
async def delete_customer(
    customer_id: int,
    db: Session = Depends(get_db)
):
    """删除客户"""
    # 检查是否存在关联的销售记录
    sales_count = db.query(SalesRecord)\
        .filter(SalesRecord.customer_id == customer_id)\
        .count()
    
    if sales_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除该客户，存在{sales_count}条关联的销售记录"
        )
    
    db_customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not db_customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    db.delete(db_customer)
    db.commit()
    return {"message": "客户删除成功"}

@router.get("/{customer_id}/statistics")
async def get_customer_statistics(
    customer_id: int,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取客户统计信息"""
    # 检查客户是否存在
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 构建查询
    query = db.query(
        func.count(SalesRecord.id).label('total_orders'),
        func.sum(SalesRecord.net_ton).label('total_weight'),
        func.avg(SalesRecord.net_ton).label('avg_weight_per_order')
    ).filter(SalesRecord.customer_id == customer_id)
    
    if start_date:
        query = query.filter(SalesRecord.sale_date >= start_date)
    if end_date:
        query = query.filter(SalesRecord.sale_date <= end_date)
    
    stats = query.first()
    
    # 获取最常购买的产品类型
    product_stats = db.query(
        SalesRecord.product_size,
        func.count(SalesRecord.id).label('count')
    ).filter(
        SalesRecord.customer_id == customer_id
    ).group_by(
        SalesRecord.product_size
    ).order_by(
        func.count(SalesRecord.id).desc()
    ).first()
    
    return {
        "customer_name": customer.name,
        "total_orders": stats.total_orders or 0,
        "total_weight": float(stats.total_weight or 0),
        "avg_weight_per_order": float(stats.avg_weight_per_order or 0),
        "most_bought_product": product_stats.product_size if product_stats else None,
        "start_date": start_date,
        "end_date": end_date
    } 