import axios from 'axios'

const API_URL = '/api'

// 获取客户列表
export const getCustomersList = (params) => {
  return axios.get(`${API_URL}/customers/list`, { params })
}

// 创建新客户
export const createCustomer = (data) => {
  return axios.post(`${API_URL}/customers/`, data)
}

// 更新客户信息
export const updateCustomer = (id, data) => {
  return axios.put(`${API_URL}/customers/${id}`, data)
}

// 删除客户
export const deleteCustomer = (id) => {
  return axios.delete(`${API_URL}/customers/${id}`)
}

// 获取客户统计信息
export const getCustomerStatistics = (id, params) => {
  return axios.get(`${API_URL}/customers/${id}/statistics`, { params })
} 